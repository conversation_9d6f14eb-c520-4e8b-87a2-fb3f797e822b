classdef ArecaApp < matlab.apps.AppBase

    properties (Access = public)
        UIFigure           matlab.ui.Figure
        LoginPanel         matlab.ui.container.Panel
        SignupPanel        matlab.ui.container.Panel
        MainPanel          matlab.ui.container.Panel

        UsernameEdit       matlab.ui.control.EditField
        PasswordEdit       matlab.ui.control.EditField
        LoginButton        matlab.ui.control.Button
        SwitchToSignupBtn  matlab.ui.control.Button

        NewUserEdit        matlab.ui.control.EditField
        NewPassEdit        matlab.ui.control.EditField
        NameEdit           matlab.ui.control.EditField
        PhoneEdit          matlab.ui.control.EditField
        GmailEdit          matlab.ui.control.EditField
        PasswordButton     matlab.ui.control.Button
        RegisterButton     matlab.ui.control.Button
        SwitchToLoginBtn   matlab.ui.control.Button

        BrowseButton       matlab.ui.control.Button
        PredictButton      matlab.ui.control.Button
        LogoutButton       matlab.ui.control.Button
        UIAxes             matlab.ui.control.UIAxes
        ResultLabel        matlab.ui.control.Label
        TitleLabel         matlab.ui.control.Label
        ControlPanel       matlab.ui.container.Panel
    end

    properties (Access = private)
        img
        net
        actualPassword
        actualNewPassword
        registrationPassword
    end

    methods (Access = private)

        %% MongoDB Connection with fallback
        function db = connectToMongoDB(app)
            try
                % Check if MongoDB driver is available
                import com.mongodb.client.MongoClients
                uri = "mongodb://localhost:27017";
                client = MongoClients.create(uri);
                db = client.getDatabase("arecanut_app");
            catch ME
                % MongoDB not available, return empty
                db = [];
                warning('MongoDB not available: %s', ME.message);
            end
        end

        function available = isMongoDBAvailable(app)
            % Check if MongoDB driver is available
            try
                import com.mongodb.client.MongoClients
                import org.bson.Document
                available = true;
            catch
                available = false;
            end
        end

        function available = isMongoDBAvailable(app)
            % Check if MongoDB is available
            try
                import com.mongodb.client.MongoClients
                import org.bson.Document
                available = true;
            catch
                available = false;
            end
        end

        function hash = hashPassword(app, pass)
            md = java.security.MessageDigest.getInstance('SHA-256');
            hashBytes = md.digest(uint8(pass));
            hash = dec2hex(typecast(hashBytes, 'uint8'));
            hash = lower(join(string(hash), ''));
            hash = char(hash);
        end

        function isValid = validatePassword(app, password)
            % Password must be exactly 8 characters with:
            % - At least 1 uppercase letter
            % - At least 1 lowercase letter
            % - At least 1 number

            isValid = false;

            % Check length
            if length(password) ~= 8
                uialert(app.UIFigure, 'Password must be exactly 8 characters long.', 'Password Error');
                return;
            end

            % Check for uppercase letter
            if ~any(isstrprop(password, 'upper'))
                uialert(app.UIFigure, 'Password must contain at least 1 uppercase letter.', 'Password Error');
                return;
            end

            % Check for lowercase letter
            if ~any(isstrprop(password, 'lower'))
                uialert(app.UIFigure, 'Password must contain at least 1 lowercase letter.', 'Password Error');
                return;
            end

            % Check for number
            if ~any(isstrprop(password, 'digit'))
                uialert(app.UIFigure, 'Password must contain at least 1 number.', 'Password Error');
                return;
            end

            isValid = true;
        end

        function success = registerUserMongo(app, name, phone, gmail, username, password)
            % Real user registration using MongoDB
            success = false;

            try
                db = app.connectToMongoDB();
                collection = db.getCollection("users");
                import org.bson.Document

                % Check if username already exists
                cursor = collection.find(Document("username", username));
                if cursor.iterator().hasNext()
                    uialert(app.UIFigure, 'Username already exists!', 'Registration Error');
                    return;
                end

                % Hash password and create user document
                hashedPass = app.hashPassword(password);
                userDoc = Document("username", username) ...
                    .append("password", hashedPass) ...
                    .append("name", name) ...
                    .append("phone", phone) ...
                    .append("gmail", gmail) ...
                    .append("created_at", java.util.Date());

                % Insert user into database
                collection.insertOne(userDoc);
                success = true;

            catch ME
                if contains(ME.message, 'MongoDB connection failed')
                    uialert(app.UIFigure, 'Cannot connect to MongoDB. Please ensure MongoDB is running.', 'Database Error');
                else
                    uialert(app.UIFigure, ['Registration failed: ', ME.message], 'Error');
                end
                success = false;
            end
        end

        function success = authenticateMongo(app, username, password)
            % Real user authentication using MongoDB
            success = false;

            try
                db = app.connectToMongoDB();
                collection = db.getCollection("users");
                import org.bson.Document

                % Hash the provided password
                hashedPassword = app.hashPassword(password);

                % Query for user with matching username and password
                query = Document("username", username).append("password", hashedPassword);
                cursor = collection.find(query);

                % Check if user exists
                success = cursor.iterator().hasNext();

            catch ME
                if contains(ME.message, 'MongoDB connection failed')
                    uialert(app.UIFigure, 'Cannot connect to MongoDB. Please ensure MongoDB is running.', 'Database Error');
                else
                    % Silent fail for authentication errors
                    fprintf('Authentication error: %s\n', ME.message);
                end
                success = false;
            end
        end

        %% Panel Switching
        function showLoginPanel(app)
            app.LoginPanel.Visible = 'on';
            app.SignupPanel.Visible = 'off';
            app.MainPanel.Visible = 'off';
        end

        function showSignupPanel(app)
            app.LoginPanel.Visible = 'off';
            app.SignupPanel.Visible = 'on';
            app.MainPanel.Visible = 'off';
        end

        function showMainPanel(app)
            app.LoginPanel.Visible = 'off';
            app.SignupPanel.Visible = 'off';
            app.MainPanel.Visible = 'on';
        end

        %% Button Actions
        function LoginButtonPushed(app, ~)
            user = app.UsernameEdit.Value;
            pass = app.actualPassword; % Use actual password, not masked version
            if app.authenticateMongo(user, pass)
                app.showMainPanel();
                % Clear password fields for security
                app.PasswordEdit.Value = '';
                app.actualPassword = '';
            else
                uialert(app.UIFigure, 'Invalid username or password.', 'Login Failed');
            end
        end

        function PasswordButtonPushed(app, ~)
            try
                % Use passwordUI to get a secure password with requirements
                pw = passwordUI('Create', 'AlphaNum', 8);
                if ~isempty(pw)
                    % Validate the password meets our requirements
                    if app.validatePassword(pw)
                        app.registrationPassword = pw;
                        app.NewPassEdit.Value = repmat('*', 1, length(pw));
                        uialert(app.UIFigure, 'Password set successfully!', 'Success');
                    end
                else
                    uialert(app.UIFigure, 'Password creation cancelled.', 'Info');
                end
            catch ME
                uialert(app.UIFigure, ['Error creating password: ', ME.message], 'Error');
            end
        end

        function RegisterButtonPushed(app, ~)
            % Get all field values
            name = strtrim(app.NameEdit.Value);
            phone = strtrim(app.PhoneEdit.Value);
            gmail = strtrim(app.GmailEdit.Value);
            user = strtrim(app.NewUserEdit.Value);
            pass = app.registrationPassword; % Use password from passwordUI

            % Check if all fields are filled
            if isempty(name) || isempty(phone) || isempty(gmail) || isempty(user) || isempty(pass)
                uialert(app.UIFigure, 'Please fill in all fields: Name, Phone, Gmail, Username, and Password.', 'Missing Information');
                return;
            end

            % Convert username to lowercase automatically
            user = lower(user);
            app.NewUserEdit.Value = user; % Update the display to show lowercase

            % Validate Gmail format
            if ~contains(gmail, '@') || ~contains(gmail, '.')
                uialert(app.UIFigure, 'Please enter a valid Gmail address.', 'Invalid Gmail');
                return;
            end

            % Validate phone number (basic check for numbers)
            if ~all(isstrprop(phone, 'digit')) || length(phone) < 10
                uialert(app.UIFigure, 'Please enter a valid phone number (at least 10 digits).', 'Invalid Phone');
                return;
            end

            % Password is already validated in PasswordButtonPushed

            % Try to register the user
            if app.registerUserMongo(name, phone, gmail, user, pass)
                uialert(app.UIFigure, 'Registration successful! Please login with your new account.', 'Success');
                app.showLoginPanel();
                % Clear all fields for security
                app.NameEdit.Value = '';
                app.PhoneEdit.Value = '';
                app.GmailEdit.Value = '';
                app.NewUserEdit.Value = '';
                app.NewPassEdit.Value = '';
                app.registrationPassword = '';
                app.actualNewPassword = '';
            end
        end

        function BrowseButtonPushed(app, ~)
            [file, path] = uigetfile({'*.jpg;*.jpeg;*.png;*.bmp;*.tiff'}, 'Select an Image');
            if isequal(file, 0)
                return;
            end
            try
                fullPath = fullfile(path, file);
                img = imread(fullPath);
                img = imresize(img, [224, 224]);
                app.img = img;
                imshow(app.img, 'Parent', app.UIAxes);
                title(app.UIAxes, ['Loaded: ', file], 'FontSize', 10, 'Color', [0.3 0.3 0.3]);
                app.ResultLabel.Text = '✅ Image loaded successfully! Click "Analyze Disease" to get prediction.';
                app.ResultLabel.FontColor = [0.2 0.6 0.4];
            catch
                uialert(app.UIFigure, '❌ Error reading the image file. Please select a valid image.', 'Image Error');
                app.ResultLabel.Text = '❌ Failed to load image. Please try again with a different file.';
                app.ResultLabel.FontColor = [0.8 0.2 0.2];
            end
        end

        function PredictButtonPushed(app, ~)
            if isempty(app.img)
                uialert(app.UIFigure, 'Please load an image first using the "Choose Image File" button.', 'Input Error');
                return;
            end
            if isempty(app.net)
                uialert(app.UIFigure, 'AI model is not loaded. Please check if the model file exists.', 'Model Error');
                return;
            end
            try
                % Show processing message
                app.ResultLabel.Text = '🔄 Analyzing image... Please wait.';
                app.ResultLabel.FontColor = [0.8 0.6 0.2];
                drawnow;

                % Perform prediction
                label = classify(app.net, app.img);

                % Format result with severity level styling
                severityText = char(label);
                switch severityText
                    case 'High_Severity'
                        resultText = '🔴 HIGH SEVERITY DISEASE DETECTED';
                        resultColor = [0.8 0.2 0.2];
                        recommendation = 'Immediate treatment required!';
                    case 'Medium_Severity'
                        resultText = '🟡 MEDIUM SEVERITY DISEASE DETECTED';
                        resultColor = [0.8 0.6 0.2];
                        recommendation = 'Treatment recommended soon.';
                    case 'Low_Severity'
                        resultText = '🟢 LOW SEVERITY DISEASE DETECTED';
                        resultColor = [0.2 0.6 0.4];
                        recommendation = 'Monitor and consider preventive measures.';
                    otherwise
                        resultText = ['🔍 DETECTED: ', severityText];
                        resultColor = [0.3 0.3 0.3];
                        recommendation = 'Please consult an expert for advice.';
                end

                app.ResultLabel.Text = [resultText, ' - ', recommendation];
                app.ResultLabel.FontColor = resultColor;
                app.ResultLabel.FontWeight = 'bold';

            catch ME
                uialert(app.UIFigure, ['Prediction failed: ', ME.message], 'Prediction Error');
                app.ResultLabel.Text = '❌ Analysis failed. Please try again or contact support.';
                app.ResultLabel.FontColor = [0.8 0.2 0.2];
            end
        end

        function startupFcn(app)
            try
                data = load('resnet50_arecanut_model.mat');
                if isfield(data, 'trainedNet')
                    app.net = data.trainedNet;
                end
            catch
                uialert(app.UIFigure, 'Model not found.', 'Error');
            end

            % Initialize password storage
            app.actualPassword = '';
            app.actualNewPassword = '';
            app.registrationPassword = '';
        end

        %% Create simple rounded button effect
        function createRoundedButton(app, button, bgColor, textColor)
            % Simple rounded appearance without complex overlays
            try
                % Update button styling with enhanced appearance
                button.BackgroundColor = bgColor;
                button.FontColor = textColor;

                % Ensure button is on top
                uistack(button, 'top');

            catch
                % Fallback to standard styling
                button.BackgroundColor = bgColor;
                button.FontColor = textColor;
            end
        end

        %% Create simple rounded input field effect
        function createRoundedInput(app, inputField)
            try
                % Enhanced input styling without complex overlays
                inputField.BackgroundColor = [0.98 0.99 1];

                % Ensure input field is on top
                uistack(inputField, 'top');

            catch
                % Fallback styling
                inputField.BackgroundColor = [0.98 0.99 1];
            end
        end

        %% Maintain fixed login form size
        function maintainLoginFormSize(app)
            % Maintain fixed size for login container regardless of window size
            if isfield(app, 'LoginPanel') && isvalid(app.LoginPanel)
                % Keep login container at fixed size and centered
                try
                    loginContainers = findall(app.LoginPanel, 'Type', 'uipanel');
                    for i = 1:length(loginContainers)
                        if length(loginContainers(i).Position) >= 4 && ...
                           loginContainers(i).Position(3) == 400 && ...
                           loginContainers(i).Position(4) == 350
                            % This is our login container - keep it fixed
                            loginContainers(i).Position = [100 250 400 350];
                        end
                    end
                catch
                    % Ignore errors during resize
                end
            end
        end

        %% Password masking functions
        function passwordChanged(app, ~)
            currentText = app.PasswordEdit.Value;
            if length(currentText) < length(app.actualPassword)
                % Characters were deleted
                app.actualPassword = app.actualPassword(1:length(currentText));
            elseif length(currentText) > length(app.actualPassword)
                % Characters were added
                newChars = currentText((length(app.actualPassword)+1):end);
                app.actualPassword = [app.actualPassword, newChars];
            end
            % Display asterisks
            app.PasswordEdit.Value = repmat('*', 1, length(app.actualPassword));
        end

        function newPasswordChanged(app, ~)
            currentText = app.NewPassEdit.Value;
            if length(currentText) < length(app.actualNewPassword)
                % Characters were deleted
                app.actualNewPassword = app.actualNewPassword(1:length(currentText));
            elseif length(currentText) > length(app.actualNewPassword)
                % Characters were added
                newChars = currentText((length(app.actualNewPassword)+1):end);
                app.actualNewPassword = [app.actualNewPassword, newChars];
            end
            % Display asterisks
            app.NewPassEdit.Value = repmat('*', 1, length(app.actualNewPassword));
        end
    end

    methods (Access = public)

        function app = ArecaApp
            app.UIFigure = uifigure( ...
                'Name', '🌿 Arecanut Disease Detection - Medical AI Platform', ...
                'Position', [100 100 1200 800], ...
                'Color', [0.94 0.96 0.98], ...
                'AutoResizeChildren', 'off', ...
                'SizeChangedFcn', @(~,~) app.maintainLoginFormSize());

            %% Login Panel - Premium Medical Design
            app.LoginPanel = uipanel(app.UIFigure, ...
                'Position', [0 0 1200 800], ...
                'BackgroundColor', [0.94 0.96 0.98]);

            % Left side - Branding panel with gradient effect
            brandingPanel = uipanel(app.LoginPanel, ...
                'Position', [0 0 600 800], ...
                'BackgroundColor', [0.15 0.45 0.25], ...
                'BorderType', 'none');

            % Medical branding content
            uilabel(brandingPanel, 'Text', '🌿', ...
                'FontSize', 80, 'FontColor', [1 1 1], ...
                'HorizontalAlignment', 'center', ...
                'Position', [200 500 200 100]);

            uilabel(brandingPanel, 'Text', 'ARECANUT', ...
                'FontSize', 32, 'FontWeight', 'bold', 'FontColor', [1 1 1], ...
                'HorizontalAlignment', 'center', ...
                'Position', [100 440 400 40]);

            uilabel(brandingPanel, 'Text', 'Disease Detection Platform', ...
                'FontSize', 16, 'FontColor', [0.8 0.9 0.8], ...
                'HorizontalAlignment', 'center', ...
                'Position', [100 410 400 25]);

            uilabel(brandingPanel, 'Text', 'AI-Powered Medical Diagnosis', ...
                'FontSize', 14, 'FontColor', [0.7 0.8 0.7], ...
                'HorizontalAlignment', 'center', ...
                'Position', [100 380 400 25]);

            % Features list
            uilabel(brandingPanel, 'Text', '✓ Advanced Deep Learning Analysis', ...
                'FontSize', 12, 'FontColor', [0.9 0.95 0.9], ...
                'Position', [80 320 300 20]);
            uilabel(brandingPanel, 'Text', '✓ Real-time Disease Classification', ...
                'FontSize', 12, 'FontColor', [0.9 0.95 0.9], ...
                'Position', [80 295 300 20]);
            uilabel(brandingPanel, 'Text', '✓ Professional Medical Reports', ...
                'FontSize', 12, 'FontColor', [0.9 0.95 0.9], ...
                'Position', [80 270 300 20]);
            uilabel(brandingPanel, 'Text', '✓ Secure Patient Data Management', ...
                'FontSize', 12, 'FontColor', [0.9 0.95 0.9], ...
                'Position', [80 245 300 20]);

            % Right side - Login form with glassmorphism backdrop
            loginFormPanel = uipanel(app.LoginPanel, ...
                'Position', [600 0 600 800], ...
                'BackgroundColor', [0.96 0.97 0.99], ...  % Light backdrop
                'BorderType', 'none');

            % Compact glassmorphism login container (fixed size)
            loginContainer = uipanel(loginFormPanel, ...
                'Position', [100 250 400 350], ...  % Smaller, fixed size
                'BackgroundColor', [0.92 0.95 0.98], ...  % Light glass effect
                'BorderType', 'line', ...
                'BorderWidth', 2, ...
                'HighlightColor', [0.85 0.9 0.95]);

            % Compact welcome header
            uilabel(loginContainer, 'Text', 'Welcome Back', ...
                'FontSize', 28, 'FontWeight', 'bold', 'FontColor', [0.15 0.15 0.15], ...
                'HorizontalAlignment', 'center', ...
                'Position', [50 290 300 40]);

            uilabel(loginContainer, 'Text', 'Sign in to access your medical dashboard', ...
                'FontSize', 13, 'FontColor', [0.4 0.4 0.4], ...
                'HorizontalAlignment', 'center', ...
                'Position', [50 260 300 20]);

            % Username field with proper spacing
            uilabel(loginContainer, 'Text', 'Username', ...
                'FontSize', 11, 'FontWeight', 'bold', 'FontColor', [0.3 0.3 0.3], ...
                'Position', [50 220 100 15]);

            app.UsernameEdit = uieditfield(loginContainer, 'text', ...
                'Placeholder', 'Enter your username', ...
                'Position', [50 190 300 35], 'FontSize', 14, ...
                'BackgroundColor', [0.95 0.97 0.99], ...  % Light glass effect
                'FontColor', [0.1 0.1 0.1]);

            % Add glassmorphism effect to username field
            app.createRoundedInput(app.UsernameEdit);

            % Password field with proper spacing
            uilabel(loginContainer, 'Text', 'Password', ...
                'FontSize', 11, 'FontWeight', 'bold', 'FontColor', [0.3 0.3 0.3], ...
                'Position', [50 150 100 15]);

            app.PasswordEdit = uieditfield(loginContainer, 'text', ...
                'Placeholder', 'Enter your password', ...
                'Position', [50 120 300 35], 'FontSize', 14, ...
                'BackgroundColor', [0.95 0.97 0.99], ...  % Light glass effect
                'FontColor', [0.1 0.1 0.1], ...
                'ValueChangedFcn', @(~,~) passwordChanged(app));

            % Add glassmorphism effect to password field
            app.createRoundedInput(app.PasswordEdit);

            % Compact login button
            app.LoginButton = uibutton(loginContainer, 'push', ...
                'Text', 'SIGN IN', 'FontSize', 15, 'FontWeight', 'bold', ...
                'FontColor', [1 1 1], 'BackgroundColor', [0.15 0.45 0.25], ...  % Solid green
                'Position', [50 70 300 40], ...
                'ButtonPushedFcn', @(~,~) LoginButtonPushed(app));

            % Add glassmorphism effect to login button
            app.createRoundedButton(app.LoginButton, [0.15 0.45 0.25], [1 1 1]);

            % Compact switch to signup button
            app.SwitchToSignupBtn = uibutton(loginContainer, 'push', ...
                'Text', 'Create New Account', 'FontSize', 12, ...
                'FontColor', [0.15 0.45 0.25], 'BackgroundColor', [0.94 0.96 0.98], ...  % Light glass
                'Position', [50 20 300 30], ...
                'ButtonPushedFcn', @(~,~) app.showSignupPanel());

            % Add glassmorphism effect to signup button
            app.createRoundedButton(app.SwitchToSignupBtn, [0.94 0.96 0.98], [0.15 0.45 0.25]);

            % Ensure all buttons are on top
            uistack(app.LoginButton, 'top');
            uistack(app.SwitchToSignupBtn, 'top');

            %% Signup Panel - Glassmorphism Design
            app.SignupPanel = uipanel(app.UIFigure, ...
                'Position', [0 0 1200 800], ...
                'Visible', 'off', ...
                'BackgroundColor', [0.92 0.94 0.98]);

            % Left side - Branding panel (same as login)
            brandingPanel2 = uipanel(app.SignupPanel, ...
                'Position', [0 0 600 800], ...
                'BackgroundColor', [0.8 0.4 0.2], ...
                'BorderType', 'none');

            % Medical branding content for signup
            % Modern medical icon
            uilabel(brandingPanel2, 'Text', '🏥', ...
                'FontSize', 90, 'FontColor', [1 1 1], ...
                'HorizontalAlignment', 'center', ...
                'Position', [200 480 200 120]);

            % Main heading with better typography
            uilabel(brandingPanel2, 'Text', 'JOIN OUR', ...
                'FontSize', 36, 'FontWeight', 'bold', 'FontColor', [1 1 1], ...
                'HorizontalAlignment', 'center', ...
                'Position', [50 420 500 50]);

            % Subtitle with improved styling
            uilabel(brandingPanel2, 'Text', 'Medical AI Community', ...
                'FontSize', 18, 'FontColor', [0.95 0.85 0.75], ...
                'HorizontalAlignment', 'center', ...
                'Position', [50 385 500 30]);

            % Description with better contrast
            uilabel(brandingPanel2, 'Text', 'Advanced Healthcare Technology', ...
                'FontSize', 14, 'FontColor', [0.85 0.75 0.65], ...
                'HorizontalAlignment', 'center', ...
                'Position', [50 355 500 25]);

            % Benefits list
            uilabel(brandingPanel2, 'Text', '✓ Access to AI Diagnostic Tools', ...
                'FontSize', 12, 'FontColor', [0.95 0.9 0.85], ...
                'Position', [80 320 300 20]);
            uilabel(brandingPanel2, 'Text', '✓ Secure Medical Data Storage', ...
                'FontSize', 12, 'FontColor', [0.95 0.9 0.85], ...
                'Position', [80 295 300 20]);
            uilabel(brandingPanel2, 'Text', '✓ Professional Analysis Reports', ...
                'FontSize', 12, 'FontColor', [0.95 0.9 0.85], ...
                'Position', [80 270 300 20]);
            uilabel(brandingPanel2, 'Text', '✓ 24/7 Platform Access', ...
                'FontSize', 12, 'FontColor', [0.95 0.9 0.85], ...
                'Position', [80 245 300 20]);

            % Right side - Signup form with glassmorphism backdrop
            signupFormPanel = uipanel(app.SignupPanel, ...
                'Position', [600 0 600 800], ...
                'BackgroundColor', [0.96 0.97 0.99], ...  % Light backdrop
                'BorderType', 'none');

            % Glassmorphism signup container
            signupContainer = uipanel(signupFormPanel, ...
                'Position', [80 30 440 740], ...
                'BackgroundColor', [0.92 0.95 0.98], ...  % Light glass effect
                'BorderType', 'line', ...
                'BorderWidth', 2, ...
                'HighlightColor', [0.85 0.9 0.95]);

            % Modern header with better spacing
            uilabel(signupContainer, 'Text', 'Create Account', ...
                'FontSize', 32, 'FontWeight', 'bold', 'FontColor', [0.15 0.15 0.15], ...
                'HorizontalAlignment', 'center', ...
                'Position', [20 680 400 45]);

            uilabel(signupContainer, 'Text', 'Join our medical AI platform today', ...
                'FontSize', 14, 'FontColor', [0.5 0.5 0.5], ...
                'HorizontalAlignment', 'center', ...
                'Position', [20 650 400 25]);

            % Name field with better spacing
            uilabel(signupContainer, 'Text', 'Full Name', ...
                'FontSize', 12, 'FontWeight', 'bold', 'FontColor', [0.3 0.3 0.3], ...
                'Position', [40 600 120 20]);

            app.NameEdit = uieditfield(signupContainer, 'text', ...
                'Placeholder', 'Enter your full name', ...
                'Position', [40 570 360 35], 'FontSize', 14, ...
                'BackgroundColor', [0.95 0.97 0.99], ...  % Light glass effect
                'FontColor', [0.1 0.1 0.1]);

            app.createRoundedInput(app.NameEdit);

            % Phone field with glassmorphism
            uilabel(signupContainer, 'Text', 'Phone Number', ...
                'FontSize', 12, 'FontWeight', 'bold', 'FontColor', [0.3 0.3 0.3], ...
                'Position', [40 520 120 20]);

            app.PhoneEdit = uieditfield(signupContainer, 'text', ...
                'Placeholder', 'Enter your phone number', ...
                'Position', [40 490 360 35], 'FontSize', 14, ...
                'BackgroundColor', [0.95 0.97 0.99], ...  % Light glass effect
                'FontColor', [0.1 0.1 0.1]);

            app.createRoundedInput(app.PhoneEdit);

            % Gmail field with glassmorphism
            uilabel(signupContainer, 'Text', 'Gmail Address', ...
                'FontSize', 12, 'FontWeight', 'bold', 'FontColor', [0.3 0.3 0.3], ...
                'Position', [40 440 120 20]);

            app.GmailEdit = uieditfield(signupContainer, 'text', ...
                'Placeholder', 'Enter your gmail address', ...
                'Position', [40 410 360 35], 'FontSize', 14, ...
                'BackgroundColor', [0.95 0.97 0.99], ...  % Light glass effect
                'FontColor', [0.1 0.1 0.1]);

            app.createRoundedInput(app.GmailEdit);

            % Username field with better spacing
            uilabel(signupContainer, 'Text', 'Username', ...
                'FontSize', 12, 'FontWeight', 'bold', 'FontColor', [0.3 0.3 0.3], ...
                'Position', [40 360 120 20]);

            uilabel(signupContainer, 'Text', '(will be converted to lowercase)', ...
                'FontSize', 10, 'FontColor', [0.6 0.6 0.6], ...
                'Position', [40 345 250 15]);

            app.NewUserEdit = uieditfield(signupContainer, 'text', ...
                'Placeholder', 'Choose your username', ...
                'Position', [40 315 360 35], 'FontSize', 14, ...
                'BackgroundColor', [0.95 0.97 0.99], ...  % Light glass effect
                'FontColor', [0.1 0.1 0.1]);

            app.createRoundedInput(app.NewUserEdit);

            % Password field with glassmorphism
            uilabel(signupContainer, 'Text', 'Password', ...
                'FontSize', 12, 'FontWeight', 'bold', 'FontColor', [0.3 0.3 0.3], ...
                'Position', [40 265 120 20]);

            uilabel(signupContainer, 'Text', '8 characters: 1 uppercase, 1 lowercase, 1 number', ...
                'FontSize', 10, 'FontColor', [0.6 0.6 0.6], ...
                'Position', [40 250 320 15]);

            app.NewPassEdit = uieditfield(signupContainer, 'text', ...
                'Placeholder', 'Click "Set Password" to create secure password', ...
                'Position', [40 220 250 35], 'FontSize', 14, ...
                'BackgroundColor', [0.95 0.97 0.99], ...  % Light glass effect
                'FontColor', [0.1 0.1 0.1], ...
                'Editable', 'off');

            app.createRoundedInput(app.NewPassEdit);

            % Glassmorphism password button
            app.PasswordButton = uibutton(signupContainer, 'push', ...
                'Text', 'Set Password', 'FontSize', 12, 'FontWeight', 'bold', ...
                'FontColor', [1 1 1], 'BackgroundColor', [0.2 0.6 0.9], ...  % Solid blue
                'Position', [300 220 100 35], ...
                'ButtonPushedFcn', @(~,~) PasswordButtonPushed(app));

            app.createRoundedButton(app.PasswordButton, [0.2 0.6 0.9], [1 1 1]);

            % Terms and conditions with proper spacing
            uilabel(signupContainer, 'Text', 'By creating an account, you agree to our Terms & Privacy Policy', ...
                'FontSize', 11, 'FontColor', [0.6 0.6 0.6], ...
                'HorizontalAlignment', 'center', ...
                'Position', [40 170 360 20]);

            % Glassmorphism register button
            app.RegisterButton = uibutton(signupContainer, 'push', ...
                'Text', 'CREATE ACCOUNT', 'FontSize', 15, 'FontWeight', 'bold', ...
                'FontColor', [1 1 1], 'BackgroundColor', [0.9 0.5 0.2], ...  % Solid orange
                'Position', [40 125 360 40], ...
                'ButtonPushedFcn', @(~,~) RegisterButtonPushed(app));

            % Add glassmorphism effect to register button
            app.createRoundedButton(app.RegisterButton, [0.9 0.5 0.2], [1 1 1]);

            % Glassmorphism switch to login button
            app.SwitchToLoginBtn = uibutton(signupContainer, 'push', ...
                'Text', 'Already have an account? Sign In', 'FontSize', 12, ...
                'FontColor', [0.9 0.5 0.2], 'BackgroundColor', [0.94 0.96 0.98], ...  % Light glass
                'Position', [40 75 360 35], ...
                'ButtonPushedFcn', @(~,~) app.showLoginPanel());

            app.createRoundedButton(app.SwitchToLoginBtn, [0.94 0.96 0.98], [0.9 0.5 0.2]);

            % Add rounded effect to login switch button and ensure visibility
            app.createRoundedButton(app.SwitchToLoginBtn, [0.98 0.98 0.98], [0.8 0.4 0.2]);

            % Ensure all signup buttons are on top
            uistack(app.RegisterButton, 'top');
            uistack(app.SwitchToLoginBtn, 'top');

            %% Main Panel - Premium Medical Dashboard
            app.MainPanel = uipanel(app.UIFigure, ...
                'Position', [0 0 1200 800], 'Visible', 'off', ...
                'BackgroundColor', [0.94 0.96 0.98]);

            % Header section with premium styling
            headerPanel = uipanel(app.MainPanel, ...
                'Position', [0 720 1200 80], ...
                'BackgroundColor', [0.15 0.45 0.25], ...
                'BorderType', 'none');

            app.TitleLabel = uilabel(headerPanel, ...
                'Text', '🌿 ARECANUT DISEASE DETECTION PLATFORM', ...
                'FontSize', 22, 'FontWeight', 'bold', 'FontColor', [1 1 1], ...
                'HorizontalAlignment', 'center', ...
                'Position', [300 25 600 30]);

            % User info and logout section
            uilabel(headerPanel, 'Text', 'Medical AI Dashboard', ...
                'FontSize', 12, 'FontColor', [0.8 0.9 0.8], ...
                'Position', [50 45 200 20]);

            app.LogoutButton = uibutton(headerPanel, 'push', ...
                'Text', 'LOGOUT', 'FontSize', 11, 'FontWeight', 'bold', ...
                'FontColor', [0.15 0.45 0.25], 'BackgroundColor', [1 1 1], ...
                'Position', [1080 25 100 30], ...
                'ButtonPushedFcn', @(~,~) app.showLoginPanel());

            % Add rounded effect to logout button
            app.createRoundedButton(app.LogoutButton, [1 1 1], [0.15 0.45 0.25]);

            % Image display area with premium styling
            imagePanel = uipanel(app.MainPanel, ...
                'Position', [50 250 550 400], ...
                'BackgroundColor', [0.98 0.99 1], ...
                'BorderType', 'line', ...
                'BorderWidth', 1, ...
                'Title', 'MEDICAL IMAGE ANALYSIS', ...
                'FontSize', 12, 'FontWeight', 'bold', 'TitlePosition', 'centertop');

            app.UIAxes = uiaxes(imagePanel, ...
                'Position', [25 25 500 330], 'XTick', [], 'YTick', [], ...
                'BackgroundColor', [0.96 0.97 0.98]);
            title(app.UIAxes, 'Upload arecanut leaf image for AI analysis', 'FontSize', 11, 'Color', [0.5 0.5 0.5]);

            % Control panel with premium design
            app.ControlPanel = uipanel(app.MainPanel, ...
                'Position', [650 250 500 400], ...
                'BackgroundColor', [0.98 0.99 1], ...
                'BorderType', 'line', ...
                'BorderWidth', 1, ...
                'Title', 'DIAGNOSTIC CONTROLS', ...
                'FontSize', 12, 'FontWeight', 'bold', 'TitlePosition', 'centertop');

            % Instructions
            uilabel(app.ControlPanel, ...
                'Text', 'Follow these steps to analyze your arecanut leaf:', ...
                'FontSize', 11, 'FontColor', [0.4 0.4 0.4], ...
                'Position', [20 320 300 20]);

            % Step 1
            uilabel(app.ControlPanel, ...
                'Text', '1. Upload Image', ...
                'FontSize', 12, 'FontWeight', 'bold', 'FontColor', [0.3 0.3 0.3], ...
                'Position', [20 280 150 20]);

            app.BrowseButton = uibutton(app.ControlPanel, 'push', ...
                'Text', '� Choose Image File', 'FontSize', 13, 'FontWeight', 'bold', ...
                'FontColor', [1 1 1], 'BackgroundColor', [0.3 0.6 0.8], ...
                'Position', [20 250 300 35], ...
                'ButtonPushedFcn', @(~,~) BrowseButtonPushed(app));

            % Step 2
            uilabel(app.ControlPanel, ...
                'Text', '2. Analyze Disease', ...
                'FontSize', 12, 'FontWeight', 'bold', 'FontColor', [0.3 0.3 0.3], ...
                'Position', [20 200 150 20]);

            app.PredictButton = uibutton(app.ControlPanel, 'push', ...
                'Text', '� Analyze Disease', 'FontSize', 13, 'FontWeight', 'bold', ...
                'FontColor', [1 1 1], 'BackgroundColor', [0.8 0.4 0.2], ...
                'Position', [20 170 300 35], ...
                'ButtonPushedFcn', @(~,~) PredictButtonPushed(app));

            % Add rounded effect to browse button
            app.createRoundedButton(app.BrowseButton, [0.3 0.6 0.8], [1 1 1]);

            % Add rounded effect to predict button
            app.createRoundedButton(app.PredictButton, [0.8 0.4 0.2], [1 1 1]);

            % Results section with premium styling
            resultsPanel = uipanel(app.MainPanel, ...
                'Position', [50 50 1100 180], ...
                'BackgroundColor', [0.98 1 0.98], ...
                'BorderType', 'line', ...
                'BorderWidth', 1, ...
                'Title', 'DIAGNOSTIC RESULTS & RECOMMENDATIONS', ...
                'FontSize', 12, 'FontWeight', 'bold', 'TitlePosition', 'centertop');

            app.ResultLabel = uilabel(resultsPanel, ...
                'Text', 'Ready for medical image analysis. Upload an image and run AI diagnostic analysis.', ...
                'FontSize', 13, 'FontColor', [0.4 0.4 0.4], ...
                'FontWeight', 'normal', ...
                'Position', [30 80 1040 40]);

            %% Load Model and Start at Login
            startupFcn(app);
            app.showLoginPanel();
        end
    end
end
