classdef ArecaApp < matlab.apps.AppBase

    properties (Access = public)
        UIFigure           matlab.ui.Figure
        LoginPanel         matlab.ui.container.Panel
        SignupPanel        matlab.ui.container.Panel
        MainPanel          matlab.ui.container.Panel

        UsernameEdit       matlab.ui.control.EditField
        PasswordEdit       matlab.ui.control.EditField
        LoginButton        matlab.ui.control.Button
        SwitchToSignupBtn  matlab.ui.control.Button

        NewUserEdit        matlab.ui.control.EditField
        NewPassEdit        matlab.ui.control.EditField
        NameEdit           matlab.ui.control.EditField
        PhoneEdit          matlab.ui.control.EditField
        GmailEdit          matlab.ui.control.EditField
        PasswordButton     matlab.ui.control.Button
        RegisterButton     matlab.ui.control.Button
        SwitchToLoginBtn   matlab.ui.control.Button

        BrowseButton       matlab.ui.control.Button
        PredictButton      matlab.ui.control.Button
        LogoutButton       matlab.ui.control.Button
        UIAxes             matlab.ui.control.UIAxes
        ResultLabel        matlab.ui.control.Label
        TitleLabel         matlab.ui.control.Label
        ControlPanel       matlab.ui.container.Panel
    end

    properties (Access = private)
        img
        net
        actualPassword
        actualNewPassword
        registrationPassword
    end

    methods (Access = private)

        %% MongoDB Connection
        function db = connectToMongoDB(app)
            import com.mongodb.client.MongoClients
            uri = "mongodb://localhost:27017";
            client = MongoClients.create(uri);
            db = client.getDatabase("arecanut_app");
        end

        function hash = hashPassword(app, pass)
            md = java.security.MessageDigest.getInstance('SHA-256');
            hashBytes = md.digest(uint8(pass));
            hash = dec2hex(typecast(hashBytes, 'uint8'));
            hash = lower(join(string(hash), ''));
            hash = char(hash);
        end

        function isValid = validatePassword(app, password)
            % Password must be exactly 8 characters with:
            % - At least 1 uppercase letter
            % - At least 1 lowercase letter
            % - At least 1 number

            isValid = false;

            % Check length
            if length(password) ~= 8
                uialert(app.UIFigure, 'Password must be exactly 8 characters long.', 'Password Error');
                return;
            end

            % Check for uppercase letter
            if ~any(isstrprop(password, 'upper'))
                uialert(app.UIFigure, 'Password must contain at least 1 uppercase letter.', 'Password Error');
                return;
            end

            % Check for lowercase letter
            if ~any(isstrprop(password, 'lower'))
                uialert(app.UIFigure, 'Password must contain at least 1 lowercase letter.', 'Password Error');
                return;
            end

            % Check for number
            if ~any(isstrprop(password, 'digit'))
                uialert(app.UIFigure, 'Password must contain at least 1 number.', 'Password Error');
                return;
            end

            isValid = true;
        end

        function success = registerUserMongo(app, name, phone, gmail, username, password)
            db = app.connectToMongoDB();
            collection = db.getCollection("users");
            import org.bson.Document

            cursor = collection.find(Document("username", username));
            if cursor.iterator().hasNext()
                uialert(app.UIFigure, 'Username already exists!', 'Signup Error');
                success = false;
                return;
            end

            hashedPass = app.hashPassword(password);
            doc = Document("username", username)...
                .append("password", hashedPass)...
                .append("name", name)...
                .append("phone", phone)...
                .append("gmail", gmail);
            collection.insertOne(doc);
            success = true;
        end

        function success = authenticateMongo(app, username, password)
            try
                db = app.connectToMongoDB();
                collection = db.getCollection("users");
                import org.bson.Document

                % Debug: Check what we're trying to authenticate
                fprintf('Login attempt - Username: "%s", Password length: %d\n', username, length(password));

                hashedPass = app.hashPassword(password);
                query = Document("username", username).append("password", hashedPass);
                cursor = collection.find(query);
                success = cursor.iterator().hasNext();

                % Debug: Show result
                if success
                    fprintf('✅ Authentication successful\n');
                else
                    fprintf('❌ Authentication failed\n');
                    % Check if user exists at all
                    userQuery = Document("username", username);
                    userCursor = collection.find(userQuery);
                    if userCursor.iterator().hasNext()
                        fprintf('   User exists but password mismatch\n');
                    else
                        fprintf('   User does not exist\n');
                    end
                end

            catch ME
                fprintf('❌ Authentication error: %s\n', ME.message);
                success = false;
            end
        end

        %% Panel Switching
        function showLoginPanel(app)
            app.LoginPanel.Visible = 'on';
            app.SignupPanel.Visible = 'off';
            app.MainPanel.Visible = 'off';
        end

        function showSignupPanel(app)
            app.LoginPanel.Visible = 'off';
            app.SignupPanel.Visible = 'on';
            app.MainPanel.Visible = 'off';
        end

        function showMainPanel(app)
            app.LoginPanel.Visible = 'off';
            app.SignupPanel.Visible = 'off';
            app.MainPanel.Visible = 'on';
        end

        %% Button Actions
        function LoginButtonPushed(app, ~)
            user = strtrim(app.UsernameEdit.Value);
            pass = app.actualPassword; % Use actual password, not masked version

            % Convert username to lowercase to match registration
            user = lower(user);

            % Debug: Show what we're trying to login with
            fprintf('Login button pressed:\n');
            fprintf('  Username: "%s" (converted to lowercase)\n', user);
            fprintf('  Password field value: "%s"\n', app.PasswordEdit.Value);
            fprintf('  Actual password: "%s"\n', pass);
            fprintf('  Password length: %d\n', length(pass));

            if isempty(user) || isempty(pass)
                uialert(app.UIFigure, 'Please enter both username and password.', 'Missing Information');
                return;
            end

            if app.authenticateMongo(user, pass)
                app.showMainPanel();
                % Clear password fields for security
                app.PasswordEdit.Value = '';
                app.actualPassword = '';
                fprintf('✅ Login successful, switched to main panel\n');
            else
                % Clear password field after failed login to prevent corruption
                app.PasswordEdit.Value = '';
                app.actualPassword = '';
                uialert(app.UIFigure, 'Invalid username or password. Please try again.', 'Login Failed');
                fprintf('❌ Login failed, password field cleared\n');
            end
        end

        function PasswordButtonPushed(app, ~)
            try
                % Use passwordUI to get a secure password with requirements
                pw = passwordUI('Create', 'AlphaNum', 8);
                if ~isempty(pw)
                    % Validate the password meets our requirements
                    if app.validatePassword(pw)
                        app.registrationPassword = pw;
                        app.NewPassEdit.Value = repmat('*', 1, length(pw));
                        uialert(app.UIFigure, 'Password set successfully!', 'Success');
                    end
                else
                    uialert(app.UIFigure, 'Password creation cancelled.', 'Info');
                end
            catch ME
                uialert(app.UIFigure, ['Error creating password: ', ME.message], 'Error');
            end
        end

        function RegisterButtonPushed(app, ~)
            % Get all field values
            name = strtrim(app.NameEdit.Value);
            phone = strtrim(app.PhoneEdit.Value);
            gmail = strtrim(app.GmailEdit.Value);
            user = strtrim(app.NewUserEdit.Value);
            pass = app.registrationPassword; % Use password from passwordUI

            % Check if all fields are filled
            if isempty(name) || isempty(phone) || isempty(gmail) || isempty(user) || isempty(pass)
                uialert(app.UIFigure, 'Please fill in all fields: Name, Phone, Gmail, Username, and Password.', 'Missing Information');
                return;
            end

            % Convert username to lowercase automatically
            user = lower(user);
            app.NewUserEdit.Value = user; % Update the display to show lowercase

            % Validate Gmail format
            if ~contains(gmail, '@') || ~contains(gmail, '.')
                uialert(app.UIFigure, 'Please enter a valid Gmail address.', 'Invalid Gmail');
                return;
            end

            % Validate phone number (basic check for numbers)
            if ~all(isstrprop(phone, 'digit')) || length(phone) < 10
                uialert(app.UIFigure, 'Please enter a valid phone number (at least 10 digits).', 'Invalid Phone');
                return;
            end

            % Password is already validated in PasswordButtonPushed

            % Try to register the user
            if app.registerUserMongo(name, phone, gmail, user, pass)
                uialert(app.UIFigure, 'Registration successful! Please login with your new account.', 'Success');
                app.showLoginPanel();
                % Clear all fields for security
                app.NameEdit.Value = '';
                app.PhoneEdit.Value = '';
                app.GmailEdit.Value = '';
                app.NewUserEdit.Value = '';
                app.NewPassEdit.Value = '';
                app.registrationPassword = '';
            end
        end

        function BrowseButtonPushed(app, ~)
            [file, path] = uigetfile({'*.jpg;*.jpeg;*.png;*.bmp;*.tiff'}, 'Select an Image');
            if isequal(file, 0)
                return;
            end
            try
                fullPath = fullfile(path, file);
                img = imread(fullPath);
                img = imresize(img, [224, 224]);
                app.img = img;
                imshow(app.img, 'Parent', app.UIAxes);
                title(app.UIAxes, ['Loaded: ', file], 'FontSize', 10, 'Color', [0.3 0.3 0.3]);
                app.ResultLabel.Text = '✅ Image loaded successfully! Click "Analyze Disease" to get prediction.';
                app.ResultLabel.FontColor = [0.2 0.6 0.4];
            catch
                uialert(app.UIFigure, '❌ Error reading the image file. Please select a valid image.', 'Image Error');
                app.ResultLabel.Text = '❌ Failed to load image. Please try again with a different file.';
                app.ResultLabel.FontColor = [0.8 0.2 0.2];
            end
        end

        function PredictButtonPushed(app, ~)
            if isempty(app.img)
                uialert(app.UIFigure, 'Please load an image first using the "Choose Image File" button.', 'Input Error');
                return;
            end
            if isempty(app.net)
                uialert(app.UIFigure, 'AI model is not loaded. Please check if the model file exists.', 'Model Error');
                return;
            end
            try
                % Show processing message
                app.ResultLabel.Text = '🔄 Analyzing image... Please wait.';
                app.ResultLabel.FontColor = [0.8 0.6 0.2];
                drawnow;

                % Perform prediction
                label = classify(app.net, app.img);

                % Format result with severity level styling
                severityText = char(label);
                switch severityText
                    case 'High_Severity'
                        resultText = '🔴 HIGH SEVERITY DISEASE DETECTED';
                        resultColor = [0.8 0.2 0.2];
                        recommendation = 'Immediate treatment required!';
                    case 'Medium_Severity'
                        resultText = '🟡 MEDIUM SEVERITY DISEASE DETECTED';
                        resultColor = [0.8 0.6 0.2];
                        recommendation = 'Treatment recommended soon.';
                    case 'Low_Severity'
                        resultText = '🟢 LOW SEVERITY DISEASE DETECTED';
                        resultColor = [0.2 0.6 0.4];
                        recommendation = 'Monitor and consider preventive measures.';
                    otherwise
                        resultText = ['🔍 DETECTED: ', severityText];
                        resultColor = [0.3 0.3 0.3];
                        recommendation = 'Please consult an expert for advice.';
                end

                app.ResultLabel.Text = [resultText, ' - ', recommendation];
                app.ResultLabel.FontColor = resultColor;
                app.ResultLabel.FontWeight = 'bold';

            catch ME
                uialert(app.UIFigure, ['Prediction failed: ', ME.message], 'Prediction Error');
                app.ResultLabel.Text = '❌ Analysis failed. Please try again or contact support.';
                app.ResultLabel.FontColor = [0.8 0.2 0.2];
            end
        end

        function startupFcn(app)
            try
                data = load('resnet50_arecanut_model.mat');
                if isfield(data, 'trainedNet')
                    app.net = data.trainedNet;
                end
            catch
                uialert(app.UIFigure, 'Model not found.', 'Error');
            end

            % Initialize password storage
            app.actualPassword = '';
            app.actualNewPassword = '';
            app.registrationPassword = '';
        end

        %% Create simple rounded button effect
        function createRoundedButton(app, button, bgColor, textColor)
            % Simple rounded appearance without complex overlays
            try
                % Update button styling with enhanced appearance
                button.BackgroundColor = bgColor;
                button.FontColor = textColor;

                % Ensure button is on top
                uistack(button, 'top');

            catch
                % Fallback to standard styling
                button.BackgroundColor = bgColor;
                button.FontColor = textColor;
            end
        end

        %% Create simple rounded input field effect
        function createRoundedInput(app, inputField)
            try
                % Enhanced input styling without complex overlays
                inputField.BackgroundColor = [0.98 0.99 1];

                % Ensure input field is on top
                uistack(inputField, 'top');

            catch
                % Fallback styling
                inputField.BackgroundColor = [0.98 0.99 1];
            end
        end

        %% Password masking functions
        function passwordChanged(app, ~)
            currentText = app.PasswordEdit.Value;

            % Skip if the current text is already asterisks (to prevent infinite loop)
            if ~isempty(currentText) && all(currentText == '*')
                return;
            end

            % If field is cleared, reset actual password
            if isempty(currentText)
                app.actualPassword = '';
                return;
            end

            % If current text is shorter than stored password, user deleted characters
            if length(currentText) < length(app.actualPassword)
                app.actualPassword = app.actualPassword(1:length(currentText));
            elseif length(currentText) > length(app.actualPassword)
                % Characters were added - get only the new characters
                newChars = currentText((length(app.actualPassword)+1):end);
                app.actualPassword = [app.actualPassword, newChars];
            end

            % Display asterisks
            app.PasswordEdit.Value = repmat('*', 1, length(app.actualPassword));
        end

        function clearPasswordField(app, ~)
            % Clear password field when double-clicked
            app.PasswordEdit.Value = '';
            app.actualPassword = '';
            fprintf('🔄 Password field cleared\n');
        end

        function newPasswordChanged(app, ~)
            currentText = app.NewPassEdit.Value;
            if length(currentText) < length(app.actualNewPassword)
                % Characters were deleted
                app.actualNewPassword = app.actualNewPassword(1:length(currentText));
            elseif length(currentText) > length(app.actualNewPassword)
                % Characters were added
                newChars = currentText((length(app.actualNewPassword)+1):end);
                app.actualNewPassword = [app.actualNewPassword, newChars];
            end
            % Display asterisks
            app.NewPassEdit.Value = repmat('*', 1, length(app.actualNewPassword));
        end
    end

    methods (Access = public)

        function app = ArecaApp
            app.UIFigure = uifigure( ...
                'Name', '🌿 Arecanut Disease Detection - Medical AI Platform', ...
                'Position', [100 100 1200 800], ...
                'Color', [0.94 0.96 0.98]);

            %% Login Panel - Enhanced Glassmorphism Design
            app.LoginPanel = uipanel(app.UIFigure, ...
                'Position', [0 0 1200 800], ...
                'BackgroundColor', [0.05 0.1 0.15]);  % Dark gradient base

            % Create gradient background effect
            gradientPanel = uipanel(app.LoginPanel, ...
                'Position', [0 0 1200 800], ...
                'BackgroundColor', [0.1 0.15 0.25], ...  % Deep blue gradient
                'BorderType', 'none');

            % Left side - Enhanced branding with glassmorphism
            brandingPanel = uipanel(gradientPanel, ...
                'Position', [50 100 500 600], ...
                'BackgroundColor', [0.15 0.25 0.35], ...  % Semi-transparent dark
                'BorderType', 'line', ...
                'BorderWidth', 1, ...
                'HighlightColor', [0.3 0.5 0.7]);  % Glowing border

            % Glassmorphism overlay for branding
            brandingGlass = uipanel(brandingPanel, ...
                'Position', [20 20 460 560], ...
                'BackgroundColor', [0.2 0.3 0.4], ...  % Glass effect
                'BorderType', 'line', ...
                'BorderWidth', 1, ...
                'HighlightColor', [0.4 0.6 0.8]);

            % Enhanced medical branding content
            uilabel(brandingGlass, 'Text', '🌿', ...
                'FontSize', 100, 'FontColor', [0.7 0.9 1], ...  % Light blue glow
                'HorizontalAlignment', 'center', ...
                'Position', [180 420 100 120]);

            uilabel(brandingGlass, 'Text', 'ARECANUT', ...
                'FontSize', 42, 'FontWeight', 'bold', 'FontColor', [0.9 0.95 1], ...
                'HorizontalAlignment', 'center', ...
                'Position', [80 360 300 60]);

            uilabel(brandingGlass, 'Text', 'Disease Detection Platform', ...
                'FontSize', 18, 'FontColor', [0.8 0.9 0.95], ...
                'HorizontalAlignment', 'center', ...
                'Position', [80 320 300 30]);

            uilabel(brandingGlass, 'Text', 'AI-Powered Medical Diagnosis', ...
                'FontSize', 16, 'FontColor', [0.7 0.85 0.95], ...
                'HorizontalAlignment', 'center', ...
                'Position', [80 290 300 25]);

            % Enhanced feature highlights with glassmorphism
            featurePanel = uipanel(brandingGlass, ...
                'Position', [40 80 380 180], ...
                'BackgroundColor', [0.25 0.35 0.45], ...
                'BorderType', 'line', ...
                'BorderWidth', 1, ...
                'HighlightColor', [0.5 0.7 0.9]);

            uilabel(featurePanel, 'Text', '✨ Advanced Deep Learning Analysis', ...
                'FontSize', 14, 'FontColor', [0.8 0.9 1], ...
                'Position', [20 140 340 25]);

            uilabel(featurePanel, 'Text', '🔬 Real-time Disease Classification', ...
                'FontSize', 14, 'FontColor', [0.8 0.9 1], ...
                'Position', [20 110 340 25]);

            uilabel(featurePanel, 'Text', '📊 Professional Medical Reports', ...
                'FontSize', 14, 'FontColor', [0.8 0.9 1], ...
                'Position', [20 80 340 25]);

            uilabel(featurePanel, 'Text', '🔒 Secure Patient Data Management', ...
                'FontSize', 14, 'FontColor', [0.8 0.9 1], ...
                'Position', [20 50 340 25]);

            uilabel(featurePanel, 'Text', '🌐 Cloud-Based MongoDB Storage', ...
                'FontSize', 14, 'FontColor', [0.8 0.9 1], ...
                'Position', [20 20 340 25]);

            % Right side - Enhanced glassmorphism login form
            loginFormPanel = uipanel(gradientPanel, ...
                'Position', [650 100 500 600], ...
                'BackgroundColor', [0.15 0.25 0.35], ...  % Semi-transparent
                'BorderType', 'line', ...
                'BorderWidth', 1, ...
                'HighlightColor', [0.3 0.5 0.7]);

            % Glassmorphism login container
            loginContainer = uipanel(loginFormPanel, ...
                'Position', [50 150 400 400], ...
                'BackgroundColor', [0.2 0.3 0.4], ...  % Glass effect
                'BorderType', 'line', ...
                'BorderWidth', 2, ...
                'HighlightColor', [0.4 0.6 0.8]);  % Glowing border

            % Enhanced welcome header with glassmorphism
            uilabel(loginContainer, 'Text', '🔐 Welcome Back', ...
                'FontSize', 32, 'FontWeight', 'bold', 'FontColor', [0.9 0.95 1], ...
                'HorizontalAlignment', 'center', ...
                'Position', [50 340 300 50]);

            uilabel(loginContainer, 'Text', 'Sign in to access your medical dashboard', ...
                'FontSize', 15, 'FontColor', [0.8 0.9 0.95], ...
                'HorizontalAlignment', 'center', ...
                'Position', [50 310 300 25]);

            % Enhanced username field with glassmorphism
            uilabel(loginContainer, 'Text', '👤 USERNAME', ...
                'FontSize', 12, 'FontWeight', 'bold', 'FontColor', [0.8 0.9 1], ...
                'Position', [50 260 150 20]);

            app.UsernameEdit = uieditfield(loginContainer, 'text', ...
                'Placeholder', 'Enter your username', ...
                'Position', [50 230 300 35], 'FontSize', 14, ...
                'BackgroundColor', [0.25 0.35 0.45], ...  % Glass effect
                'FontColor', [0.9 0.95 1]);

            % Add enhanced glassmorphism effect to username field
            app.createRoundedInput(app.UsernameEdit);

            % Enhanced password field with glassmorphism
            uilabel(loginContainer, 'Text', '🔒 PASSWORD', ...
                'FontSize', 12, 'FontWeight', 'bold', 'FontColor', [0.8 0.9 1], ...
                'Position', [50 190 150 20]);

            app.PasswordEdit = uieditfield(loginContainer, 'text', ...
                'Placeholder', 'Enter your password', ...
                'Position', [50 160 250 35], 'FontSize', 14, ...
                'BackgroundColor', [0.25 0.35 0.45], ...  % Glass effect
                'FontColor', [0.9 0.95 1], ...
                'ValueChangedFcn', @(~,~) passwordChanged(app));

            % Enhanced clear password button with glassmorphism
            clearPassBtn = uibutton(loginContainer, 'push', ...
                'Text', '🗑️', 'FontSize', 14, ...
                'Position', [310 160 35 35], ...
                'BackgroundColor', [0.3 0.4 0.5], ...  % Glass effect
                'FontColor', [0.8 0.9 1], ...
                'Tooltip', 'Clear password field', ...
                'ButtonPushedFcn', @(~,~) clearPasswordField(app));

            % Add enhanced glassmorphism effect to password field
            app.createRoundedInput(app.PasswordEdit);

            % Enhanced login button with glassmorphism
            app.LoginButton = uibutton(loginContainer, 'push', ...
                'Text', '🚀 SIGN IN', 'FontSize', 16, 'FontWeight', 'bold', ...
                'FontColor', [0.9 0.95 1], 'BackgroundColor', [0.2 0.5 0.8], ...  % Blue glass
                'Position', [50 100 300 45], ...
                'ButtonPushedFcn', @(~,~) LoginButtonPushed(app));

            % Add enhanced glassmorphism effect to login button
            app.createRoundedButton(app.LoginButton, [0.2 0.5 0.8], [0.9 0.95 1]);

            % Enhanced switch to signup button with glassmorphism
            app.SwitchToSignupBtn = uibutton(loginContainer, 'push', ...
                'Text', '✨ Create New Account', 'FontSize', 13, ...
                'FontColor', [0.8 0.9 1], 'BackgroundColor', [0.3 0.4 0.5], ...  % Subtle glass
                'Position', [50 40 300 35], ...
                'ButtonPushedFcn', @(~,~) app.showSignupPanel());

            % Add enhanced glassmorphism effect to signup button
            app.createRoundedButton(app.SwitchToSignupBtn, [0.3 0.4 0.5], [0.8 0.9 1]);

            % Ensure all buttons are on top
            uistack(app.LoginButton, 'top');
            uistack(app.SwitchToSignupBtn, 'top');

            %% Signup Panel - Enhanced Glassmorphism Design
            app.SignupPanel = uipanel(app.UIFigure, ...
                'Position', [0 0 1200 800], ...
                'Visible', 'off', ...
                'BackgroundColor', [0.05 0.1 0.15]);  % Dark gradient base

            % Create gradient background for signup
            gradientPanel2 = uipanel(app.SignupPanel, ...
                'Position', [0 0 1200 800], ...
                'BackgroundColor', [0.1 0.15 0.25], ...  % Deep blue gradient
                'BorderType', 'none');

            % Left side - Enhanced signup branding with glassmorphism
            brandingPanel2 = uipanel(gradientPanel2, ...
                'Position', [50 100 500 600], ...
                'BackgroundColor', [0.25 0.15 0.35], ...  % Purple glass
                'BorderType', 'line', ...
                'BorderWidth', 1, ...
                'HighlightColor', [0.5 0.3 0.7]);  % Purple glow

            % Glassmorphism overlay for signup branding
            brandingGlass2 = uipanel(brandingPanel2, ...
                'Position', [20 20 460 560], ...
                'BackgroundColor', [0.3 0.2 0.4], ...  % Purple glass effect
                'BorderType', 'line', ...
                'BorderWidth', 1, ...
                'HighlightColor', [0.6 0.4 0.8]);

            % Enhanced signup branding content
            uilabel(brandingGlass2, 'Text', '🏥', ...
                'FontSize', 100, 'FontColor', [0.9 0.7 1], ...  % Light purple glow
                'HorizontalAlignment', 'center', ...
                'Position', [180 420 100 120]);

            uilabel(brandingGlass2, 'Text', 'JOIN OUR', ...
                'FontSize', 42, 'FontWeight', 'bold', 'FontColor', [0.95 0.9 1], ...
                'HorizontalAlignment', 'center', ...
                'Position', [80 360 300 60]);

            uilabel(brandingGlass2, 'Text', 'Medical AI Community', ...
                'FontSize', 18, 'FontColor', [0.9 0.8 0.95], ...
                'HorizontalAlignment', 'center', ...
                'Position', [80 320 300 30]);

            uilabel(brandingGlass2, 'Text', 'Advanced Healthcare Technology', ...
                'FontSize', 16, 'FontColor', [0.85 0.7 0.95], ...
                'HorizontalAlignment', 'center', ...
                'Position', [80 290 300 25]);

            % Enhanced benefits list with glassmorphism
            benefitPanel2 = uipanel(brandingGlass2, ...
                'Position', [40 80 380 180], ...
                'BackgroundColor', [0.35 0.25 0.45], ...  % Purple glass
                'BorderType', 'line', ...
                'BorderWidth', 1, ...
                'HighlightColor', [0.6 0.4 0.8]);

            uilabel(benefitPanel2, 'Text', '🔬 Access to AI Diagnostic Tools', ...
                'FontSize', 14, 'FontColor', [0.9 0.8 1], ...
                'Position', [20 140 340 25]);

            uilabel(benefitPanel2, 'Text', '🔒 Secure Medical Data Storage', ...
                'FontSize', 14, 'FontColor', [0.9 0.8 1], ...
                'Position', [20 110 340 25]);

            uilabel(benefitPanel2, 'Text', '📊 Professional Analysis Reports', ...
                'FontSize', 14, 'FontColor', [0.9 0.8 1], ...
                'Position', [20 80 340 25]);

            uilabel(benefitPanel2, 'Text', '🌐 24/7 Platform Access', ...
                'FontSize', 14, 'FontColor', [0.9 0.8 1], ...
                'Position', [20 50 340 25]);

            uilabel(benefitPanel2, 'Text', '👥 Medical AI Community', ...
                'FontSize', 14, 'FontColor', [0.9 0.8 1], ...
                'Position', [20 20 340 25]);

            % Right side - Enhanced glassmorphism signup form
            signupFormPanel = uipanel(gradientPanel2, ...
                'Position', [650 100 500 600], ...
                'BackgroundColor', [0.25 0.15 0.35], ...  % Purple glass
                'BorderType', 'line', ...
                'BorderWidth', 1, ...
                'HighlightColor', [0.5 0.3 0.7]);

            % Enhanced glassmorphism signup container
            signupContainer = uipanel(signupFormPanel, ...
                'Position', [50 50 400 500], ...
                'BackgroundColor', [0.3 0.2 0.4], ...  % Purple glass effect
                'BorderType', 'line', ...
                'BorderWidth', 2, ...
                'HighlightColor', [0.6 0.4 0.8]);  % Purple glow

            % Enhanced welcome header with glassmorphism
            uilabel(signupContainer, 'Text', '✨ Create Account', ...
                'FontSize', 32, 'FontWeight', 'bold', 'FontColor', [0.95 0.9 1], ...
                'HorizontalAlignment', 'center', ...
                'Position', [50 440 300 50]);

            uilabel(signupContainer, 'Text', 'Join our medical AI platform today', ...
                'FontSize', 15, 'FontColor', [0.9 0.8 0.95], ...
                'HorizontalAlignment', 'center', ...
                'Position', [50 410 300 25]);

            % Enhanced name field with glassmorphism
            uilabel(signupContainer, 'Text', '👤 FULL NAME', ...
                'FontSize', 12, 'FontWeight', 'bold', 'FontColor', [0.9 0.8 1], ...
                'Position', [50 370 150 20]);

            app.NameEdit = uieditfield(signupContainer, 'text', ...
                'Placeholder', 'Enter your full name', ...
                'Position', [50 340 300 30], 'FontSize', 13, ...
                'BackgroundColor', [0.35 0.25 0.45], ...  % Purple glass
                'FontColor', [0.95 0.9 1]);

            app.createRoundedInput(app.NameEdit);

            % Enhanced phone field with glassmorphism
            uilabel(signupContainer, 'Text', '📱 PHONE NUMBER', ...
                'FontSize', 12, 'FontWeight', 'bold', 'FontColor', [0.9 0.8 1], ...
                'Position', [50 310 150 20]);

            app.PhoneEdit = uieditfield(signupContainer, 'text', ...
                'Placeholder', 'Enter your phone number', ...
                'Position', [50 280 300 30], 'FontSize', 13, ...
                'BackgroundColor', [0.35 0.25 0.45], ...  % Purple glass
                'FontColor', [0.95 0.9 1]);

            app.createRoundedInput(app.PhoneEdit);

            % Enhanced gmail field with glassmorphism
            uilabel(signupContainer, 'Text', '📧 GMAIL ADDRESS', ...
                'FontSize', 12, 'FontWeight', 'bold', 'FontColor', [0.9 0.8 1], ...
                'Position', [50 250 150 20]);

            app.GmailEdit = uieditfield(signupContainer, 'text', ...
                'Placeholder', 'Enter your gmail address', ...
                'Position', [50 220 300 30], 'FontSize', 13, ...
                'BackgroundColor', [0.35 0.25 0.45], ...  % Purple glass
                'FontColor', [0.95 0.9 1]);

            app.createRoundedInput(app.GmailEdit);

            % Enhanced username field with glassmorphism
            uilabel(signupContainer, 'Text', '🔑 USERNAME (lowercase only)', ...
                'FontSize', 12, 'FontWeight', 'bold', 'FontColor', [0.9 0.8 1], ...
                'Position', [50 190 250 20]);

            app.NewUserEdit = uieditfield(signupContainer, 'text', ...
                'Placeholder', 'Choose your username', ...
                'Position', [50 160 300 30], 'FontSize', 13, ...
                'BackgroundColor', [0.35 0.25 0.45], ...  % Purple glass
                'FontColor', [0.95 0.9 1]);

            % Add rounded effect to new username field
            app.createRoundedInput(app.NewUserEdit);

            % Enhanced password field with glassmorphism
            uilabel(signupContainer, 'Text', '🔒 PASSWORD (8 chars: 1 upper, 1 lower, 1 number)', ...
                'FontSize', 11, 'FontWeight', 'bold', 'FontColor', [0.9 0.8 1], ...
                'Position', [50 130 350 20]);

            app.NewPassEdit = uieditfield(signupContainer, 'text', ...
                'Placeholder', 'Password will be set using secure dialog', ...
                'Position', [50 100 200 30], 'FontSize', 12, ...
                'BackgroundColor', [0.35 0.25 0.45], ...  % Purple glass
                'FontColor', [0.95 0.9 1], ...
                'Editable', 'off');

            app.createRoundedInput(app.NewPassEdit);

            % Enhanced password button with glassmorphism
            app.PasswordButton = uibutton(signupContainer, 'push', ...
                'Text', '🔐 SET PASSWORD', 'FontSize', 11, 'FontWeight', 'bold', ...
                'FontColor', [0.95 0.9 1], 'BackgroundColor', [0.4 0.3 0.6], ...  % Purple glass
                'Position', [260 100 90 30], ...
                'ButtonPushedFcn', @(~,~) PasswordButtonPushed(app));

            app.createRoundedButton(app.PasswordButton, [0.4 0.3 0.6], [0.95 0.9 1]);

            % Enhanced terms and conditions
            uilabel(signupContainer, 'Text', 'By creating an account, you agree to our Terms & Privacy Policy', ...
                'FontSize', 10, 'FontColor', [0.8 0.7 0.9], ...
                'HorizontalAlignment', 'center', ...
                'Position', [50 70 300 15]);

            % Enhanced register button with glassmorphism
            app.RegisterButton = uibutton(signupContainer, 'push', ...
                'Text', '🚀 CREATE ACCOUNT', 'FontSize', 15, 'FontWeight', 'bold', ...
                'FontColor', [0.95 0.9 1], 'BackgroundColor', [0.5 0.3 0.7], ...  % Purple glass
                'Position', [50 35 300 35], ...
                'ButtonPushedFcn', @(~,~) RegisterButtonPushed(app));

            % Add enhanced glassmorphism effect to register button
            app.createRoundedButton(app.RegisterButton, [0.5 0.3 0.7], [0.95 0.9 1]);

            % Enhanced switch to login button with glassmorphism
            app.SwitchToLoginBtn = uibutton(signupContainer, 'push', ...
                'Text', '🔄 Already have an account? Sign In', 'FontSize', 11, ...
                'FontColor', [0.9 0.8 1], 'BackgroundColor', [0.4 0.25 0.5], ...  % Subtle purple glass
                'Position', [50 5 300 25], ...
                'ButtonPushedFcn', @(~,~) app.showLoginPanel());

            % Add rounded effect to login switch button and ensure visibility
            app.createRoundedButton(app.SwitchToLoginBtn, [0.98 0.98 0.98], [0.8 0.4 0.2]);

            % Ensure all signup buttons are on top
            uistack(app.RegisterButton, 'top');
            uistack(app.SwitchToLoginBtn, 'top');

            %% Main Panel - Premium Medical Dashboard
            app.MainPanel = uipanel(app.UIFigure, ...
                'Position', [0 0 1200 800], 'Visible', 'off', ...
                'BackgroundColor', [0.94 0.96 0.98]);

            % Header section with premium styling
            headerPanel = uipanel(app.MainPanel, ...
                'Position', [0 720 1200 80], ...
                'BackgroundColor', [0.15 0.45 0.25], ...
                'BorderType', 'none');

            app.TitleLabel = uilabel(headerPanel, ...
                'Text', '🌿 ARECANUT DISEASE DETECTION PLATFORM', ...
                'FontSize', 22, 'FontWeight', 'bold', 'FontColor', [1 1 1], ...
                'HorizontalAlignment', 'center', ...
                'Position', [300 25 600 30]);

            % User info and logout section
            uilabel(headerPanel, 'Text', 'Medical AI Dashboard', ...
                'FontSize', 12, 'FontColor', [0.8 0.9 0.8], ...
                'Position', [50 45 200 20]);

            app.LogoutButton = uibutton(headerPanel, 'push', ...
                'Text', 'LOGOUT', 'FontSize', 11, 'FontWeight', 'bold', ...
                'FontColor', [0.15 0.45 0.25], 'BackgroundColor', [1 1 1], ...
                'Position', [1080 25 100 30], ...
                'ButtonPushedFcn', @(~,~) app.showLoginPanel());

            % Add rounded effect to logout button
            app.createRoundedButton(app.LogoutButton, [1 1 1], [0.15 0.45 0.25]);

            % Image display area with premium styling
            imagePanel = uipanel(app.MainPanel, ...
                'Position', [50 250 550 400], ...
                'BackgroundColor', [0.98 0.99 1], ...
                'BorderType', 'line', ...
                'BorderWidth', 1, ...
                'Title', 'MEDICAL IMAGE ANALYSIS', ...
                'FontSize', 12, 'FontWeight', 'bold', 'TitlePosition', 'centertop');

            app.UIAxes = uiaxes(imagePanel, ...
                'Position', [25 25 500 330], 'XTick', [], 'YTick', [], ...
                'BackgroundColor', [0.96 0.97 0.98]);
            title(app.UIAxes, 'Upload arecanut leaf image for AI analysis', 'FontSize', 11, 'Color', [0.5 0.5 0.5]);

            % Control panel with premium design
            app.ControlPanel = uipanel(app.MainPanel, ...
                'Position', [650 250 500 400], ...
                'BackgroundColor', [0.98 0.99 1], ...
                'BorderType', 'line', ...
                'BorderWidth', 1, ...
                'Title', 'DIAGNOSTIC CONTROLS', ...
                'FontSize', 12, 'FontWeight', 'bold', 'TitlePosition', 'centertop');

            % Instructions
            uilabel(app.ControlPanel, ...
                'Text', 'Follow these steps to analyze your arecanut leaf:', ...
                'FontSize', 11, 'FontColor', [0.4 0.4 0.4], ...
                'Position', [20 320 300 20]);

            % Step 1
            uilabel(app.ControlPanel, ...
                'Text', '1. Upload Image', ...
                'FontSize', 12, 'FontWeight', 'bold', 'FontColor', [0.3 0.3 0.3], ...
                'Position', [20 280 150 20]);

            app.BrowseButton = uibutton(app.ControlPanel, 'push', ...
                'Text', '� Choose Image File', 'FontSize', 13, 'FontWeight', 'bold', ...
                'FontColor', [1 1 1], 'BackgroundColor', [0.3 0.6 0.8], ...
                'Position', [20 250 300 35], ...
                'ButtonPushedFcn', @(~,~) BrowseButtonPushed(app));

            % Step 2
            uilabel(app.ControlPanel, ...
                'Text', '2. Analyze Disease', ...
                'FontSize', 12, 'FontWeight', 'bold', 'FontColor', [0.3 0.3 0.3], ...
                'Position', [20 200 150 20]);

            app.PredictButton = uibutton(app.ControlPanel, 'push', ...
                'Text', '� Analyze Disease', 'FontSize', 13, 'FontWeight', 'bold', ...
                'FontColor', [1 1 1], 'BackgroundColor', [0.8 0.4 0.2], ...
                'Position', [20 170 300 35], ...
                'ButtonPushedFcn', @(~,~) PredictButtonPushed(app));

            % Add rounded effect to browse button
            app.createRoundedButton(app.BrowseButton, [0.3 0.6 0.8], [1 1 1]);

            % Add rounded effect to predict button
            app.createRoundedButton(app.PredictButton, [0.8 0.4 0.2], [1 1 1]);

            % Results section with premium styling
            resultsPanel = uipanel(app.MainPanel, ...
                'Position', [50 50 1100 180], ...
                'BackgroundColor', [0.98 1 0.98], ...
                'BorderType', 'line', ...
                'BorderWidth', 1, ...
                'Title', 'DIAGNOSTIC RESULTS & RECOMMENDATIONS', ...
                'FontSize', 12, 'FontWeight', 'bold', 'TitlePosition', 'centertop');

            app.ResultLabel = uilabel(resultsPanel, ...
                'Text', 'Ready for medical image analysis. Upload an image and run AI diagnostic analysis.', ...
                'FontSize', 13, 'FontColor', [0.4 0.4 0.4], ...
                'FontWeight', 'normal', ...
                'Position', [30 80 1040 40]);

            %% Load Model and Start at Login
            startupFcn(app);
            app.showLoginPanel();
        end
    end
end
