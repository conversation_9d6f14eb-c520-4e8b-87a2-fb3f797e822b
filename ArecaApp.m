classdef ArecaApp < matlab.apps.AppBase

    properties (Access = public)
        UIFigure           matlab.ui.Figure
        LoginPanel         matlab.ui.container.Panel
        SignupPanel        matlab.ui.container.Panel
        MainPanel          matlab.ui.container.Panel

        UsernameEdit       matlab.ui.control.EditField
        PasswordEdit       matlab.ui.control.EditField
        LoginButton        matlab.ui.control.Button
        SwitchToSignupBtn  matlab.ui.control.Button

        NewUserEdit        matlab.ui.control.EditField
        NewPassEdit        matlab.ui.control.EditField
        NameEdit           matlab.ui.control.EditField
        PhoneEdit          matlab.ui.control.EditField
        GmailEdit          matlab.ui.control.EditField
        PasswordButton     matlab.ui.control.Button
        RegisterButton     matlab.ui.control.Button
        SwitchToLoginBtn   matlab.ui.control.Button

        BrowseButton       matlab.ui.control.Button
        PredictButton      matlab.ui.control.Button
        LogoutButton       matlab.ui.control.Button
        UIAxes             matlab.ui.control.UIAxes
        ResultLabel        matlab.ui.control.Label
        TitleLabel         matlab.ui.control.Label
        ControlPanel       matlab.ui.container.Panel
    end

    properties (Access = private)
        img
        net
        actualPassword
        actualNewPassword
        registrationPassword
        localUsers  % Local user storage (containers.Map)
    end

    methods (Access = private)

        %% Local Data Storage (MongoDB Removed)
        function loadLocalData(app)
            % Load user data from local .mat file
            if exist('arecanut_local_data.mat', 'file')
                data = load('arecanut_local_data.mat');
                if isfield(data, 'users')
                    app.localUsers = data.users;
                else
                    app.localUsers = containers.Map();
                end
            else
                app.localUsers = containers.Map();
            end
        end

        function saveLocalData(app)
            % Save user data to local .mat file
            users = app.localUsers;
            save('arecanut_local_data.mat', 'users');
        end

        function hash = hashPassword(app, pass)
            md = java.security.MessageDigest.getInstance('SHA-256');
            hashBytes = md.digest(uint8(pass));
            hash = dec2hex(typecast(hashBytes, 'uint8'));
            hash = lower(join(string(hash), ''));
            hash = char(hash);
        end

        function isValid = validatePassword(app, password)
            % Password must be exactly 8 characters with:
            % - At least 1 uppercase letter
            % - At least 1 lowercase letter
            % - At least 1 number

            isValid = false;

            % Check length
            if length(password) ~= 8
                uialert(app.UIFigure, 'Password must be exactly 8 characters long.', 'Password Error');
                return;
            end

            % Check for uppercase letter
            if ~any(isstrprop(password, 'upper'))
                uialert(app.UIFigure, 'Password must contain at least 1 uppercase letter.', 'Password Error');
                return;
            end

            % Check for lowercase letter
            if ~any(isstrprop(password, 'lower'))
                uialert(app.UIFigure, 'Password must contain at least 1 lowercase letter.', 'Password Error');
                return;
            end

            % Check for number
            if ~any(isstrprop(password, 'digit'))
                uialert(app.UIFigure, 'Password must contain at least 1 number.', 'Password Error');
                return;
            end

            isValid = true;
        end

        function success = registerUser(app, name, phone, gmail, username, password)
            % Try MongoDB first, fall back to local storage
            try
                % Try MongoDB registration
                if app.isMongoDBAvailable()
                    success = app.registerUserMongo(name, phone, gmail, username, password);
                    return;
                end
            catch
                % MongoDB failed, continue to local storage
            end

            % Local storage registration
            app.loadLocalData();

            % Check if username already exists
            if app.localUsers.isKey(username)
                uialert(app.UIFigure, 'Username already exists!', 'Signup Error');
                success = false;
                return;
            end

            % Create new user data
            hashedPass = app.hashPassword(password);
            userData = struct();
            userData.username = username;
            userData.password = hashedPass;
            userData.name = name;
            userData.phone = phone;
            userData.gmail = gmail;
            userData.created_date = datestr(now);

            % Store user data
            app.localUsers(username) = userData;

            % Save to file
            app.saveLocalData();

            fprintf('✅ User registered successfully (local): %s\n', username);
            success = true;
        end

        function success = registerUserMongo(app, name, phone, gmail, username, password)
            try
                import com.mongodb.client.MongoClients
                import org.bson.Document

                client = MongoClients.create("mongodb://localhost:27017");
                db = client.getDatabase("arecanut_app");
                collection = db.getCollection("users");

                % Check if username already exists
                cursor = collection.find(Document("username", username));
                if cursor.iterator().hasNext()
                    uialert(app.UIFigure, 'Username already exists!', 'Signup Error');
                    success = false;
                    client.close();
                    return;
                end

                % Create new user document
                hashedPass = app.hashPassword(password);
                doc = Document("username", username)...
                    .append("password", hashedPass)...
                    .append("name", name)...
                    .append("phone", phone)...
                    .append("gmail", gmail)...
                    .append("created_date", java.util.Date());

                collection.insertOne(doc);
                client.close();

                fprintf('✅ User registered successfully (MongoDB): %s\n', username);
                success = true;

            catch ME
                fprintf('❌ MongoDB registration failed: %s\n', ME.message);
                success = false;
            end
        end

        function success = authenticateUser(app, username, password)
            % Try MongoDB first, fall back to local storage
            try
                % Try MongoDB authentication
                if app.isMongoDBAvailable()
                    success = app.authenticateMongo(username, password);
                    return;
                end
            catch
                % MongoDB failed, continue to local storage
            end

            % Local storage authentication
            try
                app.loadLocalData();

                fprintf('Login attempt (local) - Username: "%s", Password length: %d\n', username, length(password));

                % Check if user exists
                if ~app.localUsers.isKey(username)
                    fprintf('❌ User does not exist: %s\n', username);
                    success = false;
                    return;
                end

                % Get user data and verify password
                userData = app.localUsers(username);
                hashedPass = app.hashPassword(password);

                if strcmp(userData.password, hashedPass)
                    fprintf('✅ Authentication successful (local): %s\n', username);
                    success = true;
                else
                    fprintf('❌ Password mismatch: %s\n', username);
                    success = false;
                end

            catch ME
                fprintf('❌ Authentication error: %s\n', ME.message);
                success = false;
            end
        end

        function success = authenticateMongo(app, username, password)
            try
                import com.mongodb.client.MongoClients
                import org.bson.Document

                fprintf('Login attempt (MongoDB) - Username: "%s", Password length: %d\n', username, length(password));

                client = MongoClients.create("mongodb://localhost:27017");
                db = client.getDatabase("arecanut_app");
                collection = db.getCollection("users");

                hashedPass = app.hashPassword(password);
                query = Document("username", username).append("password", hashedPass);
                cursor = collection.find(query);
                success = cursor.iterator().hasNext();

                client.close();

                if success
                    fprintf('✅ Authentication successful (MongoDB): %s\n', username);
                else
                    fprintf('❌ Authentication failed (MongoDB): %s\n', username);
                end

            catch ME
                fprintf('❌ MongoDB authentication error: %s\n', ME.message);
                success = false;
            end
        end

        %% Panel Switching
        function showLoginPanel(app)
            app.LoginPanel.Visible = 'on';
            app.SignupPanel.Visible = 'off';
            app.MainPanel.Visible = 'off';
        end

        function showSignupPanel(app)
            app.LoginPanel.Visible = 'off';
            app.SignupPanel.Visible = 'on';
            app.MainPanel.Visible = 'off';
        end

        function showMainPanel(app)
            app.LoginPanel.Visible = 'off';
            app.SignupPanel.Visible = 'off';
            app.MainPanel.Visible = 'on';
        end

        %% Button Actions
        function LoginButtonPushed(app, ~)
            user = strtrim(app.UsernameEdit.Value);
            pass = app.actualPassword; % Use actual password, not masked version

            % Convert username to lowercase to match registration
            user = lower(user);

            % Debug: Show what we're trying to login with
            fprintf('Login button pressed:\n');
            fprintf('  Username: "%s" (converted to lowercase)\n', user);
            fprintf('  Password field value: "%s"\n', app.PasswordEdit.Value);
            fprintf('  Actual password: "%s"\n', pass);
            fprintf('  Password length: %d\n', length(pass));

            if isempty(user) || isempty(pass)
                uialert(app.UIFigure, 'Please enter both username and password.', 'Missing Information');
                return;
            end

            if app.authenticateUser(user, pass)
                app.showMainPanel();
                % Clear password fields for security
                app.PasswordEdit.Value = '';
                app.actualPassword = '';
                fprintf('✅ Login successful, switched to main panel\n');
            else
                % Clear password field after failed login to prevent corruption
                app.PasswordEdit.Value = '';
                app.actualPassword = '';
                uialert(app.UIFigure, 'Invalid username or password. Please try again.', 'Login Failed');
                fprintf('❌ Login failed, password field cleared\n');
            end
        end

        function PasswordButtonPushed(app, ~)
            try
                % Use passwordUI to get a secure password with requirements
                pw = passwordUI('Create', 'AlphaNum', 8);
                if ~isempty(pw)
                    % Validate the password meets our requirements
                    if app.validatePassword(pw)
                        app.registrationPassword = pw;
                        app.NewPassEdit.Value = repmat('*', 1, length(pw));
                        uialert(app.UIFigure, 'Password set successfully!', 'Success');
                    end
                else
                    uialert(app.UIFigure, 'Password creation cancelled.', 'Info');
                end
            catch ME
                uialert(app.UIFigure, ['Error creating password: ', ME.message], 'Error');
            end
        end

        function RegisterButtonPushed(app, ~)
            % Get all field values
            name = strtrim(app.NameEdit.Value);
            phone = strtrim(app.PhoneEdit.Value);
            gmail = strtrim(app.GmailEdit.Value);
            user = strtrim(app.NewUserEdit.Value);
            pass = app.registrationPassword; % Use password from passwordUI

            % Check if all fields are filled
            if isempty(name) || isempty(phone) || isempty(gmail) || isempty(user) || isempty(pass)
                uialert(app.UIFigure, 'Please fill in all fields: Name, Phone, Gmail, Username, and Password.', 'Missing Information');
                return;
            end

            % Convert username to lowercase automatically
            user = lower(user);
            app.NewUserEdit.Value = user; % Update the display to show lowercase

            % Validate Gmail format
            if ~contains(gmail, '@') || ~contains(gmail, '.')
                uialert(app.UIFigure, 'Please enter a valid Gmail address.', 'Invalid Gmail');
                return;
            end

            % Validate phone number (basic check for numbers)
            if ~all(isstrprop(phone, 'digit')) || length(phone) < 10
                uialert(app.UIFigure, 'Please enter a valid phone number (at least 10 digits).', 'Invalid Phone');
                return;
            end

            % Password is already validated in PasswordButtonPushed

            % Try to register the user
            if app.registerUser(name, phone, gmail, user, pass)
                uialert(app.UIFigure, 'Registration successful! Please login with your new account.', 'Success');
                app.showLoginPanel();
                % Clear all fields for security
                app.NameEdit.Value = '';
                app.PhoneEdit.Value = '';
                app.GmailEdit.Value = '';
                app.NewUserEdit.Value = '';
                app.NewPassEdit.Value = '';
                app.registrationPassword = '';
            end
        end

        function BrowseButtonPushed(app, ~)
            [file, path] = uigetfile({'*.jpg;*.jpeg;*.png;*.bmp;*.tiff'}, 'Select an Image');
            if isequal(file, 0)
                return;
            end
            try
                fullPath = fullfile(path, file);
                img = imread(fullPath);
                img = imresize(img, [224, 224]);
                app.img = img;
                imshow(app.img, 'Parent', app.UIAxes);
                title(app.UIAxes, ['Loaded: ', file], 'FontSize', 10, 'Color', [0.3 0.3 0.3]);
                app.ResultLabel.Text = '✅ Image loaded successfully! Click "Analyze Disease" to get prediction.';
                app.ResultLabel.FontColor = [0.2 0.6 0.4];
            catch
                uialert(app.UIFigure, '❌ Error reading the image file. Please select a valid image.', 'Image Error');
                app.ResultLabel.Text = '❌ Failed to load image. Please try again with a different file.';
                app.ResultLabel.FontColor = [0.8 0.2 0.2];
            end
        end

        function PredictButtonPushed(app, ~)
            if isempty(app.img)
                uialert(app.UIFigure, 'Please load an image first using the "Choose Image File" button.', 'Input Error');
                return;
            end
            if isempty(app.net)
                uialert(app.UIFigure, 'AI model is not loaded. Please check if the model file exists.', 'Model Error');
                return;
            end
            try
                % Show processing message
                app.ResultLabel.Text = '🔄 Analyzing image... Please wait.';
                app.ResultLabel.FontColor = [0.8 0.6 0.2];
                drawnow;

                % Perform prediction
                label = classify(app.net, app.img);

                % Format result with severity level styling
                severityText = char(label);
                switch severityText
                    case 'High_Severity'
                        resultText = '🔴 HIGH SEVERITY DISEASE DETECTED';
                        resultColor = [0.8 0.2 0.2];
                        recommendation = 'Immediate treatment required!';
                    case 'Medium_Severity'
                        resultText = '🟡 MEDIUM SEVERITY DISEASE DETECTED';
                        resultColor = [0.8 0.6 0.2];
                        recommendation = 'Treatment recommended soon.';
                    case 'Low_Severity'
                        resultText = '🟢 LOW SEVERITY DISEASE DETECTED';
                        resultColor = [0.2 0.6 0.4];
                        recommendation = 'Monitor and consider preventive measures.';
                    otherwise
                        resultText = ['🔍 DETECTED: ', severityText];
                        resultColor = [0.3 0.3 0.3];
                        recommendation = 'Please consult an expert for advice.';
                end

                app.ResultLabel.Text = [resultText, ' - ', recommendation];
                app.ResultLabel.FontColor = resultColor;
                app.ResultLabel.FontWeight = 'bold';

            catch ME
                uialert(app.UIFigure, ['Prediction failed: ', ME.message], 'Prediction Error');
                app.ResultLabel.Text = '❌ Analysis failed. Please try again or contact support.';
                app.ResultLabel.FontColor = [0.8 0.2 0.2];
            end
        end

        function startupFcn(app)
            try
                data = load('resnet50_arecanut_model.mat');
                if isfield(data, 'trainedNet')
                    app.net = data.trainedNet;
                end
            catch
                uialert(app.UIFigure, 'Model not found.', 'Error');
            end

            % Initialize password storage
            app.actualPassword = '';
            app.actualNewPassword = '';
            app.registrationPassword = '';

            % Initialize local user storage
            app.localUsers = containers.Map();
        end

        %% Create simple rounded button effect
        function createRoundedButton(app, button, bgColor, textColor)
            % Simple rounded appearance without complex overlays
            try
                % Update button styling with enhanced appearance
                button.BackgroundColor = bgColor;
                button.FontColor = textColor;

                % Ensure button is on top
                uistack(button, 'top');

            catch
                % Fallback to standard styling
                button.BackgroundColor = bgColor;
                button.FontColor = textColor;
            end
        end

        %% Create simple rounded input field effect
        function createRoundedInput(app, inputField)
            try
                % Enhanced input styling without complex overlays
                inputField.BackgroundColor = [0.98 0.99 1];

                % Ensure input field is on top
                uistack(inputField, 'top');

            catch
                % Fallback styling
                inputField.BackgroundColor = [0.98 0.99 1];
            end
        end

        %% MongoDB Availability Check
        function available = isMongoDBAvailable(app)
            try
                % Try to access MongoDB classes
                javaMethod('valueOf', 'org.bson.BsonType', 'DOCUMENT');
                available = true;
            catch
                available = false;
            end
        end

        %% Local Authentication Fallback
        function success = authenticateLocal(app, username, password)
            try
                % Load local user data
                if exist('arecanut_local_data.mat', 'file')
                    data = load('arecanut_local_data.mat');
                    if isfield(data, 'users')
                        users = data.users;
                    else
                        users = containers.Map();
                    end
                else
                    users = containers.Map();
                end

                % Check if user exists and password matches
                if users.isKey(username)
                    userData = users(username);
                    hashedPass = app.hashPassword(password);
                    if strcmp(userData.password, hashedPass)
                        fprintf('✅ Local authentication successful for user: %s\n', username);
                        success = true;
                        return;
                    end
                end

                fprintf('❌ Local authentication failed for user: %s\n', username);
                success = false;

            catch ME
                fprintf('❌ Local authentication error: %s\n', ME.message);
                success = false;
            end
        end

        %% Password masking functions
        function passwordChanged(app, ~)
            currentText = app.PasswordEdit.Value;

            % Skip if the current text is already asterisks (to prevent infinite loop)
            if ~isempty(currentText) && all(currentText == '*')
                return;
            end

            % If field is cleared, reset actual password
            if isempty(currentText)
                app.actualPassword = '';
                return;
            end

            % If current text is shorter than stored password, user deleted characters
            if length(currentText) < length(app.actualPassword)
                app.actualPassword = app.actualPassword(1:length(currentText));
            elseif length(currentText) > length(app.actualPassword)
                % Characters were added - get only the new characters
                newChars = currentText((length(app.actualPassword)+1):end);
                app.actualPassword = [app.actualPassword, newChars];
            end

            % Display asterisks
            app.PasswordEdit.Value = repmat('*', 1, length(app.actualPassword));
        end

        function clearPasswordField(app, ~)
            % Clear password field when double-clicked
            app.PasswordEdit.Value = '';
            app.actualPassword = '';
            fprintf('🔄 Password field cleared\n');
        end

        function newPasswordChanged(app, ~)
            currentText = app.NewPassEdit.Value;
            if length(currentText) < length(app.actualNewPassword)
                % Characters were deleted
                app.actualNewPassword = app.actualNewPassword(1:length(currentText));
            elseif length(currentText) > length(app.actualNewPassword)
                % Characters were added
                newChars = currentText((length(app.actualNewPassword)+1):end);
                app.actualNewPassword = [app.actualNewPassword, newChars];
            end
            % Display asterisks
            app.NewPassEdit.Value = repmat('*', 1, length(app.actualNewPassword));
        end
    end

    methods (Access = public)

        function app = ArecaApp
            app.UIFigure = uifigure( ...
                'Name', '🌿 Arecanut Disease Detection - Medical AI Platform', ...
                'Position', [100 100 1200 800], ...
                'Color', [0.94 0.96 0.98]);

            %% Login Panel - Premium Medical Design
            app.LoginPanel = uipanel(app.UIFigure, ...
                'Position', [0 0 1200 800], ...
                'BackgroundColor', [0.94 0.96 0.98]);

            % Left side - Branding panel with gradient effect
            brandingPanel = uipanel(app.LoginPanel, ...
                'Position', [0 0 600 800], ...
                'BackgroundColor', [0.15 0.45 0.25], ...
                'BorderType', 'none');

            % Medical branding content
            uilabel(brandingPanel, 'Text', '🌿', ...
                'FontSize', 80, 'FontColor', [1 1 1], ...
                'HorizontalAlignment', 'center', ...
                'Position', [200 500 200 100]);

            uilabel(brandingPanel, 'Text', 'ARECANUT', ...
                'FontSize', 32, 'FontWeight', 'bold', 'FontColor', [1 1 1], ...
                'HorizontalAlignment', 'center', ...
                'Position', [100 440 400 40]);

            uilabel(brandingPanel, 'Text', 'Disease Detection Platform', ...
                'FontSize', 16, 'FontColor', [0.8 0.9 0.8], ...
                'HorizontalAlignment', 'center', ...
                'Position', [100 410 400 25]);

            uilabel(brandingPanel, 'Text', 'AI-Powered Medical Diagnosis', ...
                'FontSize', 14, 'FontColor', [0.7 0.8 0.7], ...
                'HorizontalAlignment', 'center', ...
                'Position', [100 380 400 25]);

            % Features list
            uilabel(brandingPanel, 'Text', '✓ Advanced Deep Learning Analysis', ...
                'FontSize', 12, 'FontColor', [0.9 0.95 0.9], ...
                'Position', [80 320 300 20]);
            uilabel(brandingPanel, 'Text', '✓ Real-time Disease Classification', ...
                'FontSize', 12, 'FontColor', [0.9 0.95 0.9], ...
                'Position', [80 295 300 20]);
            uilabel(brandingPanel, 'Text', '✓ Professional Medical Reports', ...
                'FontSize', 12, 'FontColor', [0.9 0.95 0.9], ...
                'Position', [80 270 300 20]);
            uilabel(brandingPanel, 'Text', '✓ Secure Patient Data Management', ...
                'FontSize', 12, 'FontColor', [0.9 0.95 0.9], ...
                'Position', [80 245 300 20]);

            % Right side - Login form
            loginFormPanel = uipanel(app.LoginPanel, ...
                'Position', [600 0 600 800], ...
                'BackgroundColor', [1 1 1], ...
                'BorderType', 'none');

            % Login container with clean styling
            loginContainer = uipanel(loginFormPanel, ...
                'Position', [100 250 400 350], ...
                'BackgroundColor', [1 1 1], ...
                'BorderType', 'line', ...
                'BorderWidth', 1, ...
                'HighlightColor', [0.9 0.9 0.9]);

            % Welcome header
            uilabel(loginContainer, 'Text', 'Welcome Back', ...
                'FontSize', 28, 'FontWeight', 'bold', 'FontColor', [0.2 0.2 0.2], ...
                'HorizontalAlignment', 'center', ...
                'Position', [50 290 300 40]);

            uilabel(loginContainer, 'Text', 'Sign in to access your medical dashboard', ...
                'FontSize', 13, 'FontColor', [0.5 0.5 0.5], ...
                'HorizontalAlignment', 'center', ...
                'Position', [50 260 300 20]);

            % Username field with modern styling
            uilabel(loginContainer, 'Text', 'USERNAME', ...
                'FontSize', 10, 'FontWeight', 'bold', 'FontColor', [0.4 0.4 0.4], ...
                'Position', [50 220 100 15]);

            app.UsernameEdit = uieditfield(loginContainer, 'text', ...
                'Placeholder', 'Enter your username', ...
                'Position', [50 190 300 35], 'FontSize', 14, ...
                'BackgroundColor', [0.97 0.98 0.99], ...
                'FontColor', [0.2 0.2 0.2]);

            % Add rounded effect to username field
            app.createRoundedInput(app.UsernameEdit);

            % Password field with modern styling
            uilabel(loginContainer, 'Text', 'PASSWORD', ...
                'FontSize', 10, 'FontWeight', 'bold', 'FontColor', [0.4 0.4 0.4], ...
                'Position', [50 150 100 15]);

            app.PasswordEdit = uieditfield(loginContainer, 'text', ...
                'Placeholder', 'Enter your password', ...
                'Position', [50 120 250 35], 'FontSize', 14, ...
                'BackgroundColor', [0.97 0.98 0.99], ...
                'FontColor', [0.2 0.2 0.2], ...
                'ValueChangedFcn', @(~,~) passwordChanged(app));

            % Add clear password button
            clearPassBtn = uibutton(loginContainer, 'push', ...
                'Text', '🗑️', 'FontSize', 12, ...
                'Position', [310 120 35 35], ...
                'BackgroundColor', [0.95 0.95 0.95], ...
                'FontColor', [0.5 0.5 0.5], ...
                'Tooltip', 'Clear password field', ...
                'ButtonPushedFcn', @(~,~) clearPasswordField(app));

            % Add rounded effect to password field
            app.createRoundedInput(app.PasswordEdit);

            % Login button with premium rounded styling
            app.LoginButton = uibutton(loginContainer, 'push', ...
                'Text', 'SIGN IN', 'FontSize', 14, 'FontWeight', 'bold', ...
                'FontColor', [1 1 1], 'BackgroundColor', [0.15 0.45 0.25], ...
                'Position', [50 65 300 40], ...
                'ButtonPushedFcn', @(~,~) LoginButtonPushed(app));

            % Add rounded effect to login button
            app.createRoundedButton(app.LoginButton, [0.15 0.45 0.25], [1 1 1]);

            % Switch to signup button with clean styling
            app.SwitchToSignupBtn = uibutton(loginContainer, 'push', ...
                'Text', 'Create New Account', 'FontSize', 12, ...
                'FontColor', [0.15 0.45 0.25], 'BackgroundColor', [0.98 0.98 0.98], ...
                'Position', [50 15 300 30], ...
                'ButtonPushedFcn', @(~,~) app.showSignupPanel());

            % Add rounded effect to signup button and ensure it's visible
            app.createRoundedButton(app.SwitchToSignupBtn, [0.98 0.98 0.98], [0.15 0.45 0.25]);

            % Ensure all buttons are on top
            uistack(app.LoginButton, 'top');
            uistack(app.SwitchToSignupBtn, 'top');

            %% Signup Panel - Premium Medical Design
            app.SignupPanel = uipanel(app.UIFigure, ...
                'Position', [0 0 1200 800], ...
                'Visible', 'off', ...
                'BackgroundColor', [0.94 0.96 0.98]);

            % Left side - Branding panel (same as login)
            brandingPanel2 = uipanel(app.SignupPanel, ...
                'Position', [0 0 600 800], ...
                'BackgroundColor', [0.8 0.4 0.2], ...
                'BorderType', 'none');

            % Medical branding content for signup
            uilabel(brandingPanel2, 'Text', '🏥', ...
                'FontSize', 80, 'FontColor', [1 1 1], ...
                'HorizontalAlignment', 'center', ...
                'Position', [200 500 200 100]);

            uilabel(brandingPanel2, 'Text', 'JOIN OUR', ...
                'FontSize', 32, 'FontWeight', 'bold', 'FontColor', [1 1 1], ...
                'HorizontalAlignment', 'center', ...
                'Position', [100 440 400 40]);

            uilabel(brandingPanel2, 'Text', 'Medical AI Community', ...
                'FontSize', 16, 'FontColor', [0.9 0.8 0.7], ...
                'HorizontalAlignment', 'center', ...
                'Position', [100 410 400 25]);

            uilabel(brandingPanel2, 'Text', 'Advanced Healthcare Technology', ...
                'FontSize', 14, 'FontColor', [0.8 0.7 0.6], ...
                'HorizontalAlignment', 'center', ...
                'Position', [100 380 400 25]);

            % Benefits list
            uilabel(brandingPanel2, 'Text', '✓ Access to AI Diagnostic Tools', ...
                'FontSize', 12, 'FontColor', [0.95 0.9 0.85], ...
                'Position', [80 320 300 20]);
            uilabel(brandingPanel2, 'Text', '✓ Secure Medical Data Storage', ...
                'FontSize', 12, 'FontColor', [0.95 0.9 0.85], ...
                'Position', [80 295 300 20]);
            uilabel(brandingPanel2, 'Text', '✓ Professional Analysis Reports', ...
                'FontSize', 12, 'FontColor', [0.95 0.9 0.85], ...
                'Position', [80 270 300 20]);
            uilabel(brandingPanel2, 'Text', '✓ 24/7 Platform Access', ...
                'FontSize', 12, 'FontColor', [0.95 0.9 0.85], ...
                'Position', [80 245 300 20]);

            % Right side - Signup form
            signupFormPanel = uipanel(app.SignupPanel, ...
                'Position', [600 0 600 800], ...
                'BackgroundColor', [1 1 1], ...
                'BorderType', 'none');

            % Signup container with clean styling (made taller for more fields)
            signupContainer = uipanel(signupFormPanel, ...
                'Position', [100 50 400 700], ...
                'BackgroundColor', [1 1 1], ...
                'BorderType', 'line', ...
                'BorderWidth', 1, ...
                'HighlightColor', [0.9 0.9 0.9]);

            % Welcome header
            uilabel(signupContainer, 'Text', 'Create Account', ...
                'FontSize', 28, 'FontWeight', 'bold', 'FontColor', [0.2 0.2 0.2], ...
                'HorizontalAlignment', 'center', ...
                'Position', [50 640 300 40]);

            uilabel(signupContainer, 'Text', 'Join our medical AI platform today', ...
                'FontSize', 13, 'FontColor', [0.5 0.5 0.5], ...
                'HorizontalAlignment', 'center', ...
                'Position', [50 610 300 20]);

            % Name field
            uilabel(signupContainer, 'Text', 'FULL NAME', ...
                'FontSize', 10, 'FontWeight', 'bold', 'FontColor', [0.4 0.4 0.4], ...
                'Position', [50 570 100 15]);

            app.NameEdit = uieditfield(signupContainer, 'text', ...
                'Placeholder', 'Enter your full name', ...
                'Position', [50 540 300 35], 'FontSize', 14, ...
                'BackgroundColor', [0.97 0.98 0.99], ...
                'FontColor', [0.2 0.2 0.2]);

            app.createRoundedInput(app.NameEdit);

            % Phone field
            uilabel(signupContainer, 'Text', 'PHONE NUMBER', ...
                'FontSize', 10, 'FontWeight', 'bold', 'FontColor', [0.4 0.4 0.4], ...
                'Position', [50 500 100 15]);

            app.PhoneEdit = uieditfield(signupContainer, 'text', ...
                'Placeholder', 'Enter your phone number', ...
                'Position', [50 470 300 35], 'FontSize', 14, ...
                'BackgroundColor', [0.97 0.98 0.99], ...
                'FontColor', [0.2 0.2 0.2]);

            app.createRoundedInput(app.PhoneEdit);

            % Gmail field
            uilabel(signupContainer, 'Text', 'GMAIL ADDRESS', ...
                'FontSize', 10, 'FontWeight', 'bold', 'FontColor', [0.4 0.4 0.4], ...
                'Position', [50 430 100 15]);

            app.GmailEdit = uieditfield(signupContainer, 'text', ...
                'Placeholder', 'Enter your gmail address', ...
                'Position', [50 400 300 35], 'FontSize', 14, ...
                'BackgroundColor', [0.97 0.98 0.99], ...
                'FontColor', [0.2 0.2 0.2]);

            app.createRoundedInput(app.GmailEdit);

            % Username field with modern styling
            uilabel(signupContainer, 'Text', 'USERNAME (lowercase only)', ...
                'FontSize', 10, 'FontWeight', 'bold', 'FontColor', [0.4 0.4 0.4], ...
                'Position', [50 360 200 15]);

            app.NewUserEdit = uieditfield(signupContainer, 'text', ...
                'Placeholder', 'Choose your username', ...
                'Position', [50 330 300 35], 'FontSize', 14, ...
                'BackgroundColor', [0.97 0.98 0.99], ...
                'FontColor', [0.2 0.2 0.2]);

            % Add rounded effect to new username field
            app.createRoundedInput(app.NewUserEdit);

            % Password field with passwordUI button
            uilabel(signupContainer, 'Text', 'PASSWORD (8 chars: 1 upper, 1 lower, 1 number)', ...
                'FontSize', 10, 'FontWeight', 'bold', 'FontColor', [0.4 0.4 0.4], ...
                'Position', [50 290 300 15]);

            app.NewPassEdit = uieditfield(signupContainer, 'text', ...
                'Placeholder', 'Password will be set using secure dialog', ...
                'Position', [50 260 200 35], 'FontSize', 14, ...
                'BackgroundColor', [0.97 0.98 0.99], ...
                'FontColor', [0.2 0.2 0.2], ...
                'Editable', 'off');

            app.createRoundedInput(app.NewPassEdit);

            % Password button using passwordUI
            app.PasswordButton = uibutton(signupContainer, 'push', ...
                'Text', 'SET PASSWORD', 'FontSize', 12, 'FontWeight', 'bold', ...
                'FontColor', [1 1 1], 'BackgroundColor', [0.2 0.6 0.8], ...
                'Position', [260 260 90 35], ...
                'ButtonPushedFcn', @(~,~) PasswordButtonPushed(app));

            app.createRoundedButton(app.PasswordButton, [0.2 0.6 0.8], [1 1 1]);

            % Terms and conditions
            uilabel(signupContainer, 'Text', 'By creating an account, you agree to our Terms & Privacy Policy', ...
                'FontSize', 10, 'FontColor', [0.6 0.6 0.6], ...
                'HorizontalAlignment', 'center', ...
                'Position', [50 210 300 20]);

            % Register button with premium rounded styling
            app.RegisterButton = uibutton(signupContainer, 'push', ...
                'Text', 'CREATE ACCOUNT', 'FontSize', 14, 'FontWeight', 'bold', ...
                'FontColor', [1 1 1], 'BackgroundColor', [0.8 0.4 0.2], ...
                'Position', [50 165 300 40], ...
                'ButtonPushedFcn', @(~,~) RegisterButtonPushed(app));

            % Add rounded effect to register button
            app.createRoundedButton(app.RegisterButton, [0.8 0.4 0.2], [1 1 1]);

            % Switch to login button with clean styling
            app.SwitchToLoginBtn = uibutton(signupContainer, 'push', ...
                'Text', 'Already have an account? Sign In', 'FontSize', 12, ...
                'FontColor', [0.8 0.4 0.2], 'BackgroundColor', [0.98 0.98 0.98], ...
                'Position', [50 115 300 30], ...
                'ButtonPushedFcn', @(~,~) app.showLoginPanel());

            % Add rounded effect to login switch button and ensure visibility
            app.createRoundedButton(app.SwitchToLoginBtn, [0.98 0.98 0.98], [0.8 0.4 0.2]);

            % Ensure all signup buttons are on top
            uistack(app.RegisterButton, 'top');
            uistack(app.SwitchToLoginBtn, 'top');

            %% Main Panel - Premium Medical Dashboard
            app.MainPanel = uipanel(app.UIFigure, ...
                'Position', [0 0 1200 800], 'Visible', 'off', ...
                'BackgroundColor', [0.94 0.96 0.98]);

            % Header section with premium styling
            headerPanel = uipanel(app.MainPanel, ...
                'Position', [0 720 1200 80], ...
                'BackgroundColor', [0.15 0.45 0.25], ...
                'BorderType', 'none');

            app.TitleLabel = uilabel(headerPanel, ...
                'Text', '🌿 ARECANUT DISEASE DETECTION PLATFORM', ...
                'FontSize', 22, 'FontWeight', 'bold', 'FontColor', [1 1 1], ...
                'HorizontalAlignment', 'center', ...
                'Position', [300 25 600 30]);

            % User info and logout section
            uilabel(headerPanel, 'Text', 'Medical AI Dashboard', ...
                'FontSize', 12, 'FontColor', [0.8 0.9 0.8], ...
                'Position', [50 45 200 20]);

            app.LogoutButton = uibutton(headerPanel, 'push', ...
                'Text', 'LOGOUT', 'FontSize', 11, 'FontWeight', 'bold', ...
                'FontColor', [0.15 0.45 0.25], 'BackgroundColor', [1 1 1], ...
                'Position', [1080 25 100 30], ...
                'ButtonPushedFcn', @(~,~) app.showLoginPanel());

            % Add rounded effect to logout button
            app.createRoundedButton(app.LogoutButton, [1 1 1], [0.15 0.45 0.25]);

            % Image display area with premium styling
            imagePanel = uipanel(app.MainPanel, ...
                'Position', [50 250 550 400], ...
                'BackgroundColor', [0.98 0.99 1], ...
                'BorderType', 'line', ...
                'BorderWidth', 1, ...
                'Title', 'MEDICAL IMAGE ANALYSIS', ...
                'FontSize', 12, 'FontWeight', 'bold', 'TitlePosition', 'centertop');

            app.UIAxes = uiaxes(imagePanel, ...
                'Position', [25 25 500 330], 'XTick', [], 'YTick', [], ...
                'BackgroundColor', [0.96 0.97 0.98]);
            title(app.UIAxes, 'Upload arecanut leaf image for AI analysis', 'FontSize', 11, 'Color', [0.5 0.5 0.5]);

            % Control panel with premium design
            app.ControlPanel = uipanel(app.MainPanel, ...
                'Position', [650 250 500 400], ...
                'BackgroundColor', [0.98 0.99 1], ...
                'BorderType', 'line', ...
                'BorderWidth', 1, ...
                'Title', 'DIAGNOSTIC CONTROLS', ...
                'FontSize', 12, 'FontWeight', 'bold', 'TitlePosition', 'centertop');

            % Instructions
            uilabel(app.ControlPanel, ...
                'Text', 'Follow these steps to analyze your arecanut leaf:', ...
                'FontSize', 11, 'FontColor', [0.4 0.4 0.4], ...
                'Position', [20 320 300 20]);

            % Step 1
            uilabel(app.ControlPanel, ...
                'Text', '1. Upload Image', ...
                'FontSize', 12, 'FontWeight', 'bold', 'FontColor', [0.3 0.3 0.3], ...
                'Position', [20 280 150 20]);

            app.BrowseButton = uibutton(app.ControlPanel, 'push', ...
                'Text', '� Choose Image File', 'FontSize', 13, 'FontWeight', 'bold', ...
                'FontColor', [1 1 1], 'BackgroundColor', [0.3 0.6 0.8], ...
                'Position', [20 250 300 35], ...
                'ButtonPushedFcn', @(~,~) BrowseButtonPushed(app));

            % Step 2
            uilabel(app.ControlPanel, ...
                'Text', '2. Analyze Disease', ...
                'FontSize', 12, 'FontWeight', 'bold', 'FontColor', [0.3 0.3 0.3], ...
                'Position', [20 200 150 20]);

            app.PredictButton = uibutton(app.ControlPanel, 'push', ...
                'Text', '� Analyze Disease', 'FontSize', 13, 'FontWeight', 'bold', ...
                'FontColor', [1 1 1], 'BackgroundColor', [0.8 0.4 0.2], ...
                'Position', [20 170 300 35], ...
                'ButtonPushedFcn', @(~,~) PredictButtonPushed(app));

            % Add rounded effect to browse button
            app.createRoundedButton(app.BrowseButton, [0.3 0.6 0.8], [1 1 1]);

            % Add rounded effect to predict button
            app.createRoundedButton(app.PredictButton, [0.8 0.4 0.2], [1 1 1]);

            % Results section with premium styling
            resultsPanel = uipanel(app.MainPanel, ...
                'Position', [50 50 1100 180], ...
                'BackgroundColor', [0.98 1 0.98], ...
                'BorderType', 'line', ...
                'BorderWidth', 1, ...
                'Title', 'DIAGNOSTIC RESULTS & RECOMMENDATIONS', ...
                'FontSize', 12, 'FontWeight', 'bold', 'TitlePosition', 'centertop');

            app.ResultLabel = uilabel(resultsPanel, ...
                'Text', 'Ready for medical image analysis. Upload an image and run AI diagnostic analysis.', ...
                'FontSize', 13, 'FontColor', [0.4 0.4 0.4], ...
                'FontWeight', 'normal', ...
                'Position', [30 80 1040 40]);

            %% Load Model and Start at Login
            startupFcn(app);
            app.showLoginPanel();
        end
    end
end
