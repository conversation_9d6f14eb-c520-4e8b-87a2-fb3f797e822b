classdef ArecaApp < matlab.apps.AppBase

    properties (Access = public)
        UIFigure           matlab.ui.Figure
        LoginPanel         matlab.ui.container.Panel
        SignupPanel        matlab.ui.container.Panel
        MainPanel          matlab.ui.container.Panel

        UsernameEdit       matlab.ui.control.EditField
        PasswordEdit       matlab.ui.control.EditField
        LoginButton        matlab.ui.control.Button
        SwitchToSignupBtn  matlab.ui.control.Button

        NewUserEdit        matlab.ui.control.EditField
        NewPassEdit        matlab.ui.control.EditField
        NameEdit           matlab.ui.control.EditField
        PhoneEdit          matlab.ui.control.EditField
        GmailEdit          matlab.ui.control.EditField
        PasswordButton     matlab.ui.control.Button
        RegisterButton     matlab.ui.control.Button
        SwitchToLoginBtn   matlab.ui.control.Button

        BrowseButton       matlab.ui.control.Button
        PredictButton      matlab.ui.control.Button
        LogoutButton       matlab.ui.control.Button
        UIAxes             matlab.ui.control.UIAxes
        ResultLabel        matlab.ui.control.Label
        TitleLabel         matlab.ui.control.Label
        SolutionPanel      matlab.ui.container.Panel
        SolutionText       matlab.ui.control.TextArea
        ControlPanel       matlab.ui.container.Panel
    end

    properties (Access = private)
        img
        net
        actualPassword
        actualNewPassword
        registrationPassword
    end

    methods (Access = private)

        %% MongoDB Connection
        function db = connectToMongoDB(app)
            import com.mongodb.client.MongoClients
            uri = "mongodb://localhost:27017";
            client = MongoClients.create(uri);
            db = client.getDatabase("arecanut_app");
        end

        function hash = hashPassword(app, pass)
            md = java.security.MessageDigest.getInstance('SHA-256');
            hashBytes = md.digest(uint8(pass));
            hash = dec2hex(typecast(hashBytes, 'uint8'));
            hash = lower(join(string(hash), ''));
            hash = char(hash);
        end

        function success = registerUserMongo(app, username, password)
            db = app.connectToMongoDB();
            collection = db.getCollection("users");
            import org.bson.Document

            cursor = collection.find(Document("username", username));
            if cursor.iterator().hasNext()
                uialert(app.UIFigure, 'Username already exists!', 'Signup Error');
                success = false;
                return;
            end

            hashedPass = app.hashPassword(password);
            doc = Document("username", username).append("password", hashedPass);
            collection.insertOne(doc);
            success = true;
        end

        function success = authenticateMongo(app, username, password)
            db = app.connectToMongoDB();
            collection = db.getCollection("users");
            import org.bson.Document

            hashedPass = app.hashPassword(password);
            query = Document("username", username).append("password", hashedPass);
            cursor = collection.find(query);
            success = cursor.iterator().hasNext();
        end

        %% Panel Switching
        function showLoginPanel(app)
            app.LoginPanel.Visible = 'on';
            app.SignupPanel.Visible = 'off';
            app.MainPanel.Visible = 'off';
        end

        function showSignupPanel(app)
            app.LoginPanel.Visible = 'off';
            app.SignupPanel.Visible = 'on';
            app.MainPanel.Visible = 'off';
        end

        function showMainPanel(app)
            app.LoginPanel.Visible = 'off';
            app.SignupPanel.Visible = 'off';
            app.MainPanel.Visible = 'on';
        end

        %% Button Actions
        function LoginButtonPushed(app, ~)
            user = app.UsernameEdit.Value;
            pass = app.actualPassword; % Use actual password, not masked version
            if app.authenticateMongo(user, pass)
                app.showMainPanel();
                % Clear password fields for security
                app.PasswordEdit.Value = '';
                app.actualPassword = '';
            else
                uialert(app.UIFigure, 'Invalid username or password.', 'Login Failed');
            end
        end

        function RegisterButtonPushed(app, ~)
            user = app.NewUserEdit.Value;
            pass = app.actualNewPassword; % Use actual password, not masked version
            if isempty(user) || isempty(pass)
                uialert(app.UIFigure, 'Please enter all fields.', 'Error');
                return;
            end
            if app.registerUserMongo(user, pass)
                uialert(app.UIFigure, 'Registration successful. Please login.', 'Success');
                app.showLoginPanel();
                % Clear password fields for security
                app.NewPassEdit.Value = '';
                app.actualNewPassword = '';
            end
        end

        function BrowseButtonPushed(app, ~)
            [file, path] = uigetfile({'*.jpg;*.jpeg;*.png;*.bmp;*.tiff'}, 'Select an Image');
            if isequal(file, 0)
                return;
            end
            try
                fullPath = fullfile(path, file);
                img = imread(fullPath);
                img = imresize(img, [224, 224]);
                app.img = img;
                imshow(app.img, 'Parent', app.UIAxes);
                title(app.UIAxes, ['Loaded: ', file], 'FontSize', 10, 'Color', [0.3 0.3 0.3]);
                app.ResultLabel.Text = '✅ Image loaded successfully! Click "Analyze Disease" to get prediction.';
                app.ResultLabel.FontColor = [0.2 0.6 0.4];
            catch
                uialert(app.UIFigure, '❌ Error reading the image file. Please select a valid image.', 'Image Error');
                app.ResultLabel.Text = '❌ Failed to load image. Please try again with a different file.';
                app.ResultLabel.FontColor = [0.8 0.2 0.2];
            end
        end

        function PredictButtonPushed(app, ~)
            if isempty(app.img)
                uialert(app.UIFigure, 'Please load an image first using the "Choose Image File" button.', 'Input Error');
                return;
            end
            if isempty(app.net)
                uialert(app.UIFigure, 'AI model is not loaded. Please check if the model file exists.', 'Model Error');
                return;
            end
            try
                % Show processing message
                app.SolutionText.Value = {'🔄 ANALYZING IMAGE...', '', 'Please wait while our AI analyzes the arecanut leaf image.', 'This may take a few moments.'};
                drawnow;

                % Perform prediction
                label = classify(app.net, app.img);

                % Format result with detailed treatment information
                severityText = char(label);
                app.updateSolutionPanel(severityText);

            catch ME
                uialert(app.UIFigure, ['Prediction failed: ', ME.message], 'Prediction Error');
                app.SolutionText.Value = {'❌ ANALYSIS FAILED', '', 'Error occurred during analysis:', ME.message, '', 'Please try again or contact support.'};
            end
        end

        %% Update solution panel with detailed treatment information
        function updateSolutionPanel(app, diseaseType)
            switch diseaseType
                case 'Fruit_Rot'
                    app.SolutionText.Value = {
                        '🔴 FRUIT ROT DETECTED', '',
                        '📋 DISEASE INFORMATION:',
                        '• Disease: Arecanut Fruit Rot',
                        '• Severity: High',
                        '• Pathogen: Fungal infection', '',
                        '💊 TREATMENT RECOMMENDATIONS:',
                        '• Apply copper-based fungicides immediately',
                        '• Remove and destroy infected fruits',
                        '• Improve drainage around trees',
                        '• Spray Bordeaux mixture (1%) weekly', '',
                        '🌿 ORGANIC SOLUTIONS:',
                        '• Neem oil spray (2-3ml/liter)',
                        '• Trichoderma application to soil',
                        '• Proper pruning for air circulation', '',
                        '� PREVENTION MEASURES:',
                        '• Regular inspection of fruits',
                        '• Avoid overhead irrigation',
                        '• Maintain proper plant spacing',
                        '• Apply balanced fertilizers'
                    };
                case 'Leaf_Spot'
                    app.SolutionText.Value = {
                        '� LEAF SPOT DETECTED', '',
                        '📋 DISEASE INFORMATION:',
                        '• Disease: Arecanut Leaf Spot',
                        '• Severity: Medium',
                        '• Pathogen: Bacterial/Fungal infection', '',
                        '💊 TREATMENT RECOMMENDATIONS:',
                        '• Apply streptomycin sulfate (0.1%)',
                        '• Use copper oxychloride spray',
                        '• Remove affected leaves immediately',
                        '• Spray mancozeb (0.25%) fortnightly', '',
                        '🌿 ORGANIC SOLUTIONS:',
                        '• Baking soda spray (1 tsp/liter)',
                        '• Garlic extract application',
                        '• Compost tea foliar spray', '',
                        '🚨 PREVENTION MEASURES:',
                        '• Ensure good air circulation',
                        '• Avoid water on leaves',
                        '• Regular sanitation practices',
                        '• Balanced nutrition program'
                    };
                case 'Healthy'
                    app.SolutionText.Value = {
                        '✅ HEALTHY LEAF DETECTED', '',
                        '🎉 CONGRATULATIONS!',
                        'Your arecanut leaf appears to be healthy with no signs of disease.', '',
                        '🌱 MAINTENANCE RECOMMENDATIONS:',
                        '• Continue current care practices',
                        '• Regular monitoring for early detection',
                        '• Maintain proper nutrition schedule',
                        '• Ensure adequate water management', '',
                        '🔍 PREVENTIVE MEASURES:',
                        '• Monthly health inspections',
                        '• Proper pruning and sanitation',
                        '• Balanced fertilizer application',
                        '• Pest monitoring program', '',
                        '📅 NEXT STEPS:',
                        '• Schedule next inspection in 2 weeks',
                        '• Document current healthy status',
                        '• Continue preventive care routine'
                    };
                otherwise
                    app.SolutionText.Value = {
                        '🔍 ANALYSIS COMPLETE', '',
                        ['📊 DETECTED: ', diseaseType], '',
                        '⚠️ GENERAL RECOMMENDATIONS:',
                        '• Consult with agricultural expert',
                        '• Take additional photos for verification',
                        '• Monitor plant condition closely',
                        '• Consider laboratory testing', '',
                        '📞 EXPERT CONSULTATION:',
                        '• Contact local agricultural extension office',
                        '• Seek advice from plant pathologist',
                        '• Consider professional diagnosis', '',
                        '📝 DOCUMENTATION:',
                        '• Record symptoms and progression',
                        '• Take photos for reference',
                        '• Note environmental conditions'
                    };
            end
        end

        function startupFcn(app)
            try
                data = load('resnet50_arecanut_model.mat');
                if isfield(data, 'trainedNet')
                    app.net = data.trainedNet;
                end
            catch
                uialert(app.UIFigure, 'Model not found.', 'Error');
            end

            % Initialize password storage
            app.actualPassword = '';
            app.actualNewPassword = '';
        end

        %% Create simple rounded button effect
        function createRoundedButton(app, button, bgColor, textColor)
            % Simple rounded appearance without complex overlays
            try
                % Update button styling with enhanced appearance
                button.BackgroundColor = bgColor;
                button.FontColor = textColor;

                % Ensure button is on top
                uistack(button, 'top');

            catch
                % Fallback to standard styling
                button.BackgroundColor = bgColor;
                button.FontColor = textColor;
            end
        end

        %% Create simple rounded input field effect
        function createRoundedInput(app, inputField)
            try
                % Enhanced input styling without complex overlays
                inputField.BackgroundColor = [0.98 0.99 1];

                % Ensure input field is on top
                uistack(inputField, 'top');

            catch
                % Fallback styling
                inputField.BackgroundColor = [0.98 0.99 1];
            end
        end

        %% Password masking functions
        function passwordChanged(app, ~)
            currentText = app.PasswordEdit.Value;
            if length(currentText) < length(app.actualPassword)
                % Characters were deleted
                app.actualPassword = app.actualPassword(1:length(currentText));
            elseif length(currentText) > length(app.actualPassword)
                % Characters were added
                newChars = currentText((length(app.actualPassword)+1):end);
                app.actualPassword = [app.actualPassword, newChars];
            end
            % Display asterisks
            app.PasswordEdit.Value = repmat('*', 1, length(app.actualPassword));
        end

        function newPasswordChanged(app, ~)
            currentText = app.NewPassEdit.Value;
            if length(currentText) < length(app.actualNewPassword)
                % Characters were deleted
                app.actualNewPassword = app.actualNewPassword(1:length(currentText));
            elseif length(currentText) > length(app.actualNewPassword)
                % Characters were added
                newChars = currentText((length(app.actualNewPassword)+1):end);
                app.actualNewPassword = [app.actualNewPassword, newChars];
            end
            % Display asterisks
            app.NewPassEdit.Value = repmat('*', 1, length(app.actualNewPassword));
        end
    end

    methods (Access = public)

        function app = ArecaApp
            app.UIFigure = uifigure( ...
                'Name', '🌿 Arecanut Disease Detection - Medical AI Platform', ...
                'Position', [100 100 1200 800], ...
                'Color', [0.94 0.96 0.98]);

            %% Login Panel - Premium Medical Design
            app.LoginPanel = uipanel(app.UIFigure, ...
                'Position', [0 0 1200 800], ...
                'BackgroundColor', [0.94 0.96 0.98]);

            % Left side - Branding panel with gradient effect
            brandingPanel = uipanel(app.LoginPanel, ...
                'Position', [0 0 600 800], ...
                'BackgroundColor', [0.15 0.45 0.25], ...
                'BorderType', 'none');

            % Medical branding content
            uilabel(brandingPanel, 'Text', '🌿', ...
                'FontSize', 80, 'FontColor', [1 1 1], ...
                'HorizontalAlignment', 'center', ...
                'Position', [200 500 200 100]);

            uilabel(brandingPanel, 'Text', 'ARECANUT', ...
                'FontSize', 32, 'FontWeight', 'bold', 'FontColor', [1 1 1], ...
                'HorizontalAlignment', 'center', ...
                'Position', [100 440 400 40]);

            uilabel(brandingPanel, 'Text', 'Disease Detection Platform', ...
                'FontSize', 16, 'FontColor', [0.8 0.9 0.8], ...
                'HorizontalAlignment', 'center', ...
                'Position', [100 410 400 25]);

            uilabel(brandingPanel, 'Text', 'AI-Powered Medical Diagnosis', ...
                'FontSize', 14, 'FontColor', [0.7 0.8 0.7], ...
                'HorizontalAlignment', 'center', ...
                'Position', [100 380 400 25]);

            % Features list
            uilabel(brandingPanel, 'Text', '✓ Advanced Deep Learning Analysis', ...
                'FontSize', 12, 'FontColor', [0.9 0.95 0.9], ...
                'Position', [80 320 300 20]);
            uilabel(brandingPanel, 'Text', '✓ Real-time Disease Classification', ...
                'FontSize', 12, 'FontColor', [0.9 0.95 0.9], ...
                'Position', [80 295 300 20]);
            uilabel(brandingPanel, 'Text', '✓ Professional Medical Reports', ...
                'FontSize', 12, 'FontColor', [0.9 0.95 0.9], ...
                'Position', [80 270 300 20]);
            uilabel(brandingPanel, 'Text', '✓ Secure Patient Data Management', ...
                'FontSize', 12, 'FontColor', [0.9 0.95 0.9], ...
                'Position', [80 245 300 20]);

            % Right side - Login form
            loginFormPanel = uipanel(app.LoginPanel, ...
                'Position', [600 0 600 800], ...
                'BackgroundColor', [1 1 1], ...
                'BorderType', 'none');

            % Login container with clean styling
            loginContainer = uipanel(loginFormPanel, ...
                'Position', [100 250 400 350], ...
                'BackgroundColor', [1 1 1], ...
                'BorderType', 'line', ...
                'BorderWidth', 1, ...
                'HighlightColor', [0.9 0.9 0.9]);

            % Welcome header
            uilabel(loginContainer, 'Text', 'Welcome Back', ...
                'FontSize', 28, 'FontWeight', 'bold', 'FontColor', [0.2 0.2 0.2], ...
                'HorizontalAlignment', 'center', ...
                'Position', [50 290 300 40]);

            uilabel(loginContainer, 'Text', 'Sign in to access your medical dashboard', ...
                'FontSize', 13, 'FontColor', [0.5 0.5 0.5], ...
                'HorizontalAlignment', 'center', ...
                'Position', [50 260 300 20]);

            % Username field with modern styling
            uilabel(loginContainer, 'Text', 'USERNAME', ...
                'FontSize', 10, 'FontWeight', 'bold', 'FontColor', [0.4 0.4 0.4], ...
                'Position', [50 220 100 15]);

            app.UsernameEdit = uieditfield(loginContainer, 'text', ...
                'Placeholder', 'Enter your username', ...
                'Position', [50 190 300 35], 'FontSize', 14, ...
                'BackgroundColor', [0.97 0.98 0.99], ...
                'FontColor', [0.2 0.2 0.2]);

            % Add rounded effect to username field
            app.createRoundedInput(app.UsernameEdit);

            % Password field with modern styling
            uilabel(loginContainer, 'Text', 'PASSWORD', ...
                'FontSize', 10, 'FontWeight', 'bold', 'FontColor', [0.4 0.4 0.4], ...
                'Position', [50 150 100 15]);

            app.PasswordEdit = uieditfield(loginContainer, 'text', ...
                'Placeholder', 'Enter your password', ...
                'Position', [50 120 300 35], 'FontSize', 14, ...
                'BackgroundColor', [0.97 0.98 0.99], ...
                'FontColor', [0.2 0.2 0.2], ...
                'ValueChangedFcn', @(~,~) passwordChanged(app));

            % Add rounded effect to password field
            app.createRoundedInput(app.PasswordEdit);

            % Login button with premium rounded styling
            app.LoginButton = uibutton(loginContainer, 'push', ...
                'Text', 'SIGN IN', 'FontSize', 14, 'FontWeight', 'bold', ...
                'FontColor', [1 1 1], 'BackgroundColor', [0.15 0.45 0.25], ...
                'Position', [50 65 300 40], ...
                'ButtonPushedFcn', @(~,~) LoginButtonPushed(app));

            % Add rounded effect to login button
            app.createRoundedButton(app.LoginButton, [0.15 0.45 0.25], [1 1 1]);

            % Switch to signup button with clean styling
            app.SwitchToSignupBtn = uibutton(loginContainer, 'push', ...
                'Text', 'Create New Account', 'FontSize', 12, ...
                'FontColor', [0.15 0.45 0.25], 'BackgroundColor', [0.98 0.98 0.98], ...
                'Position', [50 15 300 30], ...
                'ButtonPushedFcn', @(~,~) app.showSignupPanel());

            % Add rounded effect to signup button and ensure it's visible
            app.createRoundedButton(app.SwitchToSignupBtn, [0.98 0.98 0.98], [0.15 0.45 0.25]);

            % Ensure all buttons are on top
            uistack(app.LoginButton, 'top');
            uistack(app.SwitchToSignupBtn, 'top');

            %% Signup Panel - Premium Medical Design
            app.SignupPanel = uipanel(app.UIFigure, ...
                'Position', [0 0 1200 800], ...
                'Visible', 'off', ...
                'BackgroundColor', [0.94 0.96 0.98]);

            % Left side - Branding panel (same as login)
            brandingPanel2 = uipanel(app.SignupPanel, ...
                'Position', [0 0 600 800], ...
                'BackgroundColor', [0.8 0.4 0.2], ...
                'BorderType', 'none');

            % Medical branding content for signup
            uilabel(brandingPanel2, 'Text', '🏥', ...
                'FontSize', 80, 'FontColor', [1 1 1], ...
                'HorizontalAlignment', 'center', ...
                'Position', [200 500 200 100]);

            uilabel(brandingPanel2, 'Text', 'JOIN OUR', ...
                'FontSize', 32, 'FontWeight', 'bold', 'FontColor', [1 1 1], ...
                'HorizontalAlignment', 'center', ...
                'Position', [100 440 400 40]);

            uilabel(brandingPanel2, 'Text', 'Medical AI Community', ...
                'FontSize', 16, 'FontColor', [0.9 0.8 0.7], ...
                'HorizontalAlignment', 'center', ...
                'Position', [100 410 400 25]);

            uilabel(brandingPanel2, 'Text', 'Advanced Healthcare Technology', ...
                'FontSize', 14, 'FontColor', [0.8 0.7 0.6], ...
                'HorizontalAlignment', 'center', ...
                'Position', [100 380 400 25]);

            % Benefits list
            uilabel(brandingPanel2, 'Text', '✓ Access to AI Diagnostic Tools', ...
                'FontSize', 12, 'FontColor', [0.95 0.9 0.85], ...
                'Position', [80 320 300 20]);
            uilabel(brandingPanel2, 'Text', '✓ Secure Medical Data Storage', ...
                'FontSize', 12, 'FontColor', [0.95 0.9 0.85], ...
                'Position', [80 295 300 20]);
            uilabel(brandingPanel2, 'Text', '✓ Professional Analysis Reports', ...
                'FontSize', 12, 'FontColor', [0.95 0.9 0.85], ...
                'Position', [80 270 300 20]);
            uilabel(brandingPanel2, 'Text', '✓ 24/7 Platform Access', ...
                'FontSize', 12, 'FontColor', [0.95 0.9 0.85], ...
                'Position', [80 245 300 20]);

            % Right side - Signup form
            signupFormPanel = uipanel(app.SignupPanel, ...
                'Position', [600 0 600 800], ...
                'BackgroundColor', [1 1 1], ...
                'BorderType', 'none');

            % Signup container with clean styling
            signupContainer = uipanel(signupFormPanel, ...
                'Position', [100 220 400 400], ...
                'BackgroundColor', [1 1 1], ...
                'BorderType', 'line', ...
                'BorderWidth', 1, ...
                'HighlightColor', [0.9 0.9 0.9]);

            % Welcome header
            uilabel(signupContainer, 'Text', 'Create Account', ...
                'FontSize', 28, 'FontWeight', 'bold', 'FontColor', [0.2 0.2 0.2], ...
                'HorizontalAlignment', 'center', ...
                'Position', [50 340 300 40]);

            uilabel(signupContainer, 'Text', 'Join our medical AI platform today', ...
                'FontSize', 13, 'FontColor', [0.5 0.5 0.5], ...
                'HorizontalAlignment', 'center', ...
                'Position', [50 310 300 20]);

            % Username field with modern styling
            uilabel(signupContainer, 'Text', 'USERNAME', ...
                'FontSize', 10, 'FontWeight', 'bold', 'FontColor', [0.4 0.4 0.4], ...
                'Position', [50 270 100 15]);

            app.NewUserEdit = uieditfield(signupContainer, 'text', ...
                'Placeholder', 'Choose your username', ...
                'Position', [50 240 300 35], 'FontSize', 14, ...
                'BackgroundColor', [0.97 0.98 0.99], ...
                'FontColor', [0.2 0.2 0.2]);

            % Add rounded effect to new username field
            app.createRoundedInput(app.NewUserEdit);

            % Password field with modern styling
            uilabel(signupContainer, 'Text', 'PASSWORD', ...
                'FontSize', 10, 'FontWeight', 'bold', 'FontColor', [0.4 0.4 0.4], ...
                'Position', [50 200 100 15]);

            app.NewPassEdit = uieditfield(signupContainer, 'text', ...
                'Placeholder', 'Create a secure password', ...
                'Position', [50 170 300 35], 'FontSize', 14, ...
                'BackgroundColor', [0.97 0.98 0.99], ...
                'FontColor', [0.2 0.2 0.2], ...
                'ValueChangedFcn', @(~,~) newPasswordChanged(app));

            % Add rounded effect to new password field
            app.createRoundedInput(app.NewPassEdit);

            % Terms and conditions
            uilabel(signupContainer, 'Text', 'By creating an account, you agree to our Terms & Privacy Policy', ...
                'FontSize', 10, 'FontColor', [0.6 0.6 0.6], ...
                'HorizontalAlignment', 'center', ...
                'Position', [50 130 300 20]);

            % Register button with premium rounded styling
            app.RegisterButton = uibutton(signupContainer, 'push', ...
                'Text', 'CREATE ACCOUNT', 'FontSize', 14, 'FontWeight', 'bold', ...
                'FontColor', [1 1 1], 'BackgroundColor', [0.8 0.4 0.2], ...
                'Position', [50 85 300 40], ...
                'ButtonPushedFcn', @(~,~) RegisterButtonPushed(app));

            % Add rounded effect to register button
            app.createRoundedButton(app.RegisterButton, [0.8 0.4 0.2], [1 1 1]);

            % Switch to login button with clean styling
            app.SwitchToLoginBtn = uibutton(signupContainer, 'push', ...
                'Text', 'Already have an account? Sign In', 'FontSize', 12, ...
                'FontColor', [0.8 0.4 0.2], 'BackgroundColor', [0.98 0.98 0.98], ...
                'Position', [50 35 300 30], ...
                'ButtonPushedFcn', @(~,~) app.showLoginPanel());

            % Add rounded effect to login switch button and ensure visibility
            app.createRoundedButton(app.SwitchToLoginBtn, [0.98 0.98 0.98], [0.8 0.4 0.2]);

            % Ensure all signup buttons are on top
            uistack(app.RegisterButton, 'top');
            uistack(app.SwitchToLoginBtn, 'top');

            %% Main Panel - Premium Medical Dashboard
            app.MainPanel = uipanel(app.UIFigure, ...
                'Position', [0 0 1200 800], 'Visible', 'off', ...
                'BackgroundColor', [0.94 0.96 0.98]);

            % Header section with premium styling
            headerPanel = uipanel(app.MainPanel, ...
                'Position', [0 720 1200 80], ...
                'BackgroundColor', [0.15 0.45 0.25], ...
                'BorderType', 'none');

            app.TitleLabel = uilabel(headerPanel, ...
                'Text', '🌿 ARECANUT DISEASE DETECTION PLATFORM', ...
                'FontSize', 22, 'FontWeight', 'bold', 'FontColor', [1 1 1], ...
                'HorizontalAlignment', 'center', ...
                'Position', [300 25 600 30]);

            % User info and logout section
            uilabel(headerPanel, 'Text', 'Medical AI Dashboard', ...
                'FontSize', 12, 'FontColor', [0.8 0.9 0.8], ...
                'Position', [50 45 200 20]);

            app.LogoutButton = uibutton(headerPanel, 'push', ...
                'Text', 'LOGOUT', 'FontSize', 11, 'FontWeight', 'bold', ...
                'FontColor', [0.15 0.45 0.25], 'BackgroundColor', [1 1 1], ...
                'Position', [1080 25 100 30], ...
                'ButtonPushedFcn', @(~,~) app.showLoginPanel());

            % Add rounded effect to logout button
            app.createRoundedButton(app.LogoutButton, [1 1 1], [0.15 0.45 0.25]);

            % Left side - Image Analysis Area (60% width)
            leftPanel = uipanel(app.MainPanel, ...
                'Position', [30 50 720 600], ...
                'BackgroundColor', [0.98 0.99 1], ...
                'BorderType', 'line', ...
                'BorderWidth', 1, ...
                'Title', 'MEDICAL IMAGE ANALYSIS', ...
                'FontSize', 12, 'FontWeight', 'bold', 'TitlePosition', 'centertop');

            % Image display area within left panel
            app.UIAxes = uiaxes(leftPanel, ...
                'Position', [25 200 670 350], 'XTick', [], 'YTick', [], ...
                'BackgroundColor', [0.96 0.97 0.98]);
            title(app.UIAxes, 'Upload arecanut leaf image for AI analysis', 'FontSize', 11, 'Color', [0.5 0.5 0.5]);

            % Control buttons within left panel
            uilabel(leftPanel, 'Text', '1. Upload Image', ...
                'FontSize', 12, 'FontWeight', 'bold', 'FontColor', [0.3 0.3 0.3], ...
                'Position', [25 150 150 20]);

            app.BrowseButton = uibutton(leftPanel, 'push', ...
                'Text', 'Choose Image File', 'FontSize', 13, 'FontWeight', 'bold', ...
                'FontColor', [1 1 1], 'BackgroundColor', [0.3 0.6 0.8], ...
                'Position', [25 120 300 35], ...
                'ButtonPushedFcn', @(~,~) BrowseButtonPushed(app));

            % Add rounded effect to browse button
            app.createRoundedButton(app.BrowseButton, [0.3 0.6 0.8], [1 1 1]);

            uilabel(leftPanel, 'Text', '2. Analyze Disease', ...
                'FontSize', 12, 'FontWeight', 'bold', 'FontColor', [0.3 0.3 0.3], ...
                'Position', [25 80 150 20]);

            app.PredictButton = uibutton(leftPanel, 'push', ...
                'Text', 'RUN ANALYSIS', 'FontSize', 13, 'FontWeight', 'bold', ...
                'FontColor', [1 1 1], 'BackgroundColor', [0.8 0.4 0.2], ...
                'Position', [25 50 300 35], ...
                'ButtonPushedFcn', @(~,~) PredictButtonPushed(app));

            % Add rounded effect to predict button
            app.createRoundedButton(app.PredictButton, [0.8 0.4 0.2], [1 1 1]);

            % Right side - Disease Solution Panel (40% width)
            app.SolutionPanel = uipanel(app.MainPanel, ...
                'Position', [770 50 400 600], ...
                'BackgroundColor', [0.98 1 0.98], ...
                'BorderType', 'line', ...
                'BorderWidth', 1, ...
                'Title', 'DISEASE SOLUTIONS & TREATMENT', ...
                'FontSize', 12, 'FontWeight', 'bold', 'TitlePosition', 'centertop');

            % Solution content area
            app.SolutionText = uitextarea(app.SolutionPanel, ...
                'Position', [20 20 360 550], ...
                'BackgroundColor', [0.99 1 0.99], ...
                'FontSize', 11, ...
                'Editable', 'off', ...
                'Value', {'Welcome to Arecanut Disease Detection Platform', '', ...
                         'Instructions:', ...
                         '1. Upload an image of arecanut leaf using the "Choose Image File" button', ...
                         '2. Click "RUN ANALYSIS" to detect diseases', ...
                         '3. View detailed treatment recommendations here', '', ...
                         'Supported Diseases:', ...
                         '• Fruit Rot', ...
                         '• Leaf Spot', ...
                         '• Healthy Leaf Detection', '', ...
                         'After analysis, this panel will show:', ...
                         '• Disease identification', ...
                         '• Severity assessment', ...
                         '• Treatment recommendations', ...
                         '• Prevention measures', ...
                         '• Chemical/organic solutions'});

            %% Load Model and Start at Login
                'Text', '� Choose Image File', 'FontSize', 13, 'FontWeight', 'bold', ...


            % Step 2
            uilabel(app.ControlPanel, ...
                'Text', '2. Analyze Disease', ...
                'FontSize', 12, 'FontWeight', 'bold', 'FontColor', [0.3 0.3 0.3], ...
                'Position', [20 200 150 20]);

            app.PredictButton = uibutton(app.ControlPanel, 'push', ...
                'Text', '� Analyze Disease', 'FontSize', 13, 'FontWeight', 'bold', ...
                'FontColor', [1 1 1], 'BackgroundColor', [0.8 0.4 0.2], ...
                'Position', [20 170 300 35], ...
                'ButtonPushedFcn', @(~,~) PredictButtonPushed(app));

            % Add rounded effect to browse button
            app.createRoundedButton(app.BrowseButton, [0.3 0.6 0.8], [1 1 1]);

            % Add rounded effect to predict button
            app.createRoundedButton(app.PredictButton, [0.8 0.4 0.2], [1 1 1]);



            %% Load Model and Start at Login
            startupFcn(app);
            app.showLoginPanel();
        end
    end
end
