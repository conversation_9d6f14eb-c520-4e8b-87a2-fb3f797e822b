classdef ArecaApp_New < matlab.apps.AppBase
    % ArecaApp_New - Modern Arecanut Disease Detection Application
    % Features organized components, landing page, and popup authentication
    
    properties (Access = public)
        UIFigure                matlab.ui.Figure
        
        % New organized components
        Config                  % App configuration
        Navbar                  % Navigation bar component
        LandingPage             % Landing page component
        LoginPopup              % Login popup component
        SignupPopup             % Signup popup component
        DetectionPage           % Detection page component
        
        % Legacy components (for backward compatibility)
        MainPanel               matlab.ui.container.Panel
        BrowseButton            matlab.ui.control.Button
        PredictButton           matlab.ui.control.Button
        LogoutButton            matlab.ui.control.Button
        UIAxes                  matlab.ui.control.UIAxes
        ResultLabel             matlab.ui.control.Label
        TitleLabel              matlab.ui.control.Label
        ControlPanel            matlab.ui.container.Panel
        
        % Application state
        CurrentUser             % Current logged-in user
        IsLoggedIn              % Login status
        CurrentPage             % Current active page
    end
    
    properties (Access = private)
        % Database and authentication
        DatabaseConnection      % MongoDB connection
        UserData               % User data storage
        ModelData              % AI model data
        
        % UI State
        SelectedImagePath      % Currently selected image
        LastPrediction         % Last prediction result
        
        % Callbacks structure
        AppCallbacks           % Application callback functions
    end
    
    methods (Access = public)
        function app = ArecaApp_New()
            % Constructor - Initialize the application
            
            % Add component paths
            addpath('components');
            addpath('pages');
            addpath('utils');
            addpath('data');
            
            % Initialize configuration
            app.Config = AppConfig();
            
            % Initialize application state
            app.IsLoggedIn = false;
            app.CurrentPage = 'landing';
            app.CurrentUser = '';
            
            % Create UI components
            app.createComponents();
            
            % Setup callbacks
            app.setupCallbacks();
            
            % Initialize database connection
            app.initializeDatabase();
            
            % Load AI model
            app.loadAIModel();
            
            % Show the app
            app.UIFigure.Visible = 'on';
            
            fprintf('🌿 Arecanut Disease Detection Platform v2.0 Loaded Successfully!\n');
            fprintf('✨ Features: Landing Page, Popup Auth, Disease Gallery, Treatment Recommendations\n');
        end
        
        function createComponents(app)
            % Create main UI components
            
            % Create main figure
            app.UIFigure = uifigure('Visible', 'off');
            app.UIFigure.Position = app.Config.WINDOW_POSITION;
            app.UIFigure.Name = app.Config.APP_NAME;
            app.UIFigure.Resize = 'off';
            app.UIFigure.CloseRequestFcn = @(~,~) app.closeApp();
            
            % Create main container
            mainContainer = uipanel(app.UIFigure, ...
                'Position', [0 0 1200 800], ...
                'BackgroundColor', app.Config.getCurrentTheme().background, ...
                'BorderType', 'none');
            
            % Initialize callback structure
            app.setupCallbacks();
            
            % Create navbar
            app.Navbar = Navbar(mainContainer, app.Config, app.AppCallbacks);
            
            % Create landing page
            app.LandingPage = LandingPage(mainContainer, app.Config, app.AppCallbacks);
            
            % Create authentication popups
            app.LoginPopup = LoginPopup(app.UIFigure, app.Config, app.AppCallbacks);
            app.SignupPopup = SignupPopup(app.UIFigure, app.Config, app.AppCallbacks);
            
            % Create detection page (initially hidden)
            app.createDetectionPage(mainContainer);
        end
        
        function setupCallbacks(app)
            % Setup application callback functions
            app.AppCallbacks = struct();
            
            % Authentication callbacks
            app.AppCallbacks.showLoginPopup = @() app.showLoginPopup();
            app.AppCallbacks.showSignupPopup = @() app.showSignupPopup();
            app.AppCallbacks.authenticate = @(username, password) app.authenticateUser(username, password);
            app.AppCallbacks.register = @(userData) app.registerUser(userData);
            app.AppCallbacks.onLoginSuccess = @(username) app.onLoginSuccess(username);
            app.AppCallbacks.onSignupSuccess = @(username) app.onSignupSuccess(username);
            app.AppCallbacks.logout = @() app.logoutUser();
            
            % Navigation callbacks
            app.AppCallbacks.scrollToSection = @(sectionId) app.scrollToSection(sectionId);
            app.AppCallbacks.showDiseaseDetails = @(diseaseType) app.showDiseaseDetails(diseaseType);
            
            % Theme callbacks
            app.AppCallbacks.updateTheme = @() app.updateAllThemes();
        end
        
        function createDetectionPage(app, parent)
            % Create detection page (simplified version of original)
            theme = app.Config.getCurrentTheme();
            
            app.MainPanel = uipanel(parent, ...
                'Position', [0 0 1200 750], ...
                'BackgroundColor', theme.background, ...
                'BorderType', 'none', ...
                'Visible', 'off');
            
            % Control panel
            app.ControlPanel = uipanel(app.MainPanel, ...
                'Position', [50 50 500 650], ...
                'BackgroundColor', theme.surface, ...
                'BorderType', 'line', ...
                'BorderWidth', 1, ...
                'HighlightColor', theme.border);
            
            % Title
            app.TitleLabel = uilabel(app.ControlPanel, ...
                'Text', '🌿 AI Disease Detection Dashboard', ...
                'FontSize', 18, 'FontWeight', 'bold', ...
                'FontColor', theme.text_primary, ...
                'Position', [20 600 460 30]);
            
            % Browse button
            app.BrowseButton = uibutton(app.ControlPanel, 'push', ...
                'Text', '📁 Select Image', ...
                'FontSize', 14, 'FontWeight', 'bold', ...
                'FontColor', [1 1 1], ...
                'BackgroundColor', theme.primary, ...
                'Position', [20 550 200 40], ...
                'ButtonPushedFcn', @(~,~) app.browseImage());
            
            % Predict button
            app.PredictButton = uibutton(app.ControlPanel, 'push', ...
                'Text', '🤖 Analyze Disease', ...
                'FontSize', 14, 'FontWeight', 'bold', ...
                'FontColor', [1 1 1], ...
                'BackgroundColor', theme.accent, ...
                'Position', [240 550 200 40], ...
                'ButtonPushedFcn', @(~,~) app.predictDisease());
            
            % Result label
            app.ResultLabel = uilabel(app.ControlPanel, ...
                'Text', 'Upload an image to get started...', ...
                'FontSize', 12, ...
                'FontColor', theme.text_secondary, ...
                'Position', [20 500 460 30]);
            
            % Logout button
            app.LogoutButton = uibutton(app.ControlPanel, 'push', ...
                'Text', '🚪 Logout', ...
                'FontSize', 12, ...
                'FontColor', [1 1 1], ...
                'BackgroundColor', [0.8 0.3 0.3], ...
                'Position', [20 20 100 30], ...
                'ButtonPushedFcn', @(~,~) app.logoutUser());
            
            % Image display axes
            app.UIAxes = uiaxes(app.MainPanel, ...
                'Position', [600 100 550 550], ...
                'BackgroundColor', theme.surface);
            app.UIAxes.Title.String = 'Uploaded Image';
            app.UIAxes.XTick = [];
            app.UIAxes.YTick = [];
        end
        
        function initializeDatabase(app)
            % Initialize database connection
            try
                % Always start with a fresh containers.Map
                app.UserData = containers.Map();

                % Load existing user data if available
                if exist('user_data.mat', 'file')
                    try
                        loadedData = load('user_data.mat');
                        if isfield(loadedData, 'userData') && isa(loadedData.userData, 'containers.Map')
                            app.UserData = loadedData.userData;
                            fprintf('✅ Existing user database loaded\n');
                        else
                            fprintf('⚠️ Invalid user data format, creating new database\n');
                        end
                    catch
                        fprintf('⚠️ Could not load user data, creating new database\n');
                    end
                else
                    fprintf('📝 Creating new user database\n');
                end

                % Add a default test user for demonstration
                if app.UserData.Count == 0
                    testUser = struct();
                    testUser.name = 'Test User';
                    testUser.email = '<EMAIL>';
                    testUser.phone = '1234567890';
                    testUser.password = 'test123';
                    app.UserData('testuser') = testUser;
                    fprintf('👤 Added default test user (username: testuser, password: test123)\n');
                end

                fprintf('✅ Database initialized successfully\n');
            catch ME
                fprintf('⚠️ Database initialization error: %s\n', ME.message);
                app.UserData = containers.Map();
                % Add default user even if there's an error
                testUser = struct();
                testUser.name = 'Test User';
                testUser.email = '<EMAIL>';
                testUser.phone = '1234567890';
                testUser.password = 'test123';
                app.UserData('testuser') = testUser;
            end
        end
        
        function loadAIModel(app)
            % Load AI model
            try
                if exist('resnet50_arecanut_model.mat', 'file')
                    load('resnet50_arecanut_model.mat', 'net');
                    app.ModelData = net;
                    fprintf('✅ AI model loaded successfully\n');
                else
                    fprintf('⚠️ AI model file not found\n');
                    app.ModelData = [];
                end
            catch ME
                fprintf('⚠️ Model loading warning: %s\n', ME.message);
                app.ModelData = [];
            end
        end
        
        function showLoginPopup(app)
            % Show login popup
            app.LoginPopup.show();
        end
        
        function showSignupPopup(app)
            % Show signup popup
            app.SignupPopup.show();
        end
        
        function success = authenticateUser(app, username, password)
            % Authenticate user credentials
            success = false;

            try
                % Check if UserData is properly initialized
                if ~isa(app.UserData, 'containers.Map')
                    fprintf('⚠️ UserData not properly initialized, reinitializing...\n');
                    app.initializeDatabase();
                end

                % Check if user exists
                if app.UserData.isKey(username)
                    userData = app.UserData(username);
                    if isfield(userData, 'password')
                        success = strcmp(userData.password, password);
                        if success
                            fprintf('✅ User %s authenticated successfully\n', username);
                        else
                            fprintf('❌ Invalid password for user %s\n', username);
                        end
                    else
                        fprintf('❌ User data corrupted for %s\n', username);
                    end
                else
                    fprintf('❌ User %s not found\n', username);
                end
            catch ME
                fprintf('❌ Authentication error: %s\n', ME.message);
                success = false;
            end
        end
        
        function success = registerUser(app, userData)
            % Register new user
            success = false;

            try
                % Check if UserData is properly initialized
                if ~isa(app.UserData, 'containers.Map')
                    fprintf('⚠️ UserData not properly initialized, reinitializing...\n');
                    app.initializeDatabase();
                end

                % Check if username already exists
                if app.UserData.isKey(userData.username)
                    fprintf('❌ Username %s already exists\n', userData.username);
                    success = false;
                else
                    % Store user data (password would be hashed in production)
                    userData.password = 'demo123'; % Default password for demo
                    app.UserData(userData.username) = userData;
                    app.saveUserData();
                    fprintf('✅ User %s registered successfully\n', userData.username);
                    success = true;
                end
            catch ME
                fprintf('❌ Registration error: %s\n', ME.message);
                success = false;
            end
        end
        
        function onLoginSuccess(app, username)
            % Handle successful login
            app.IsLoggedIn = true;
            app.CurrentUser = username;
            app.CurrentPage = 'detection';
            
            % Update navbar
            app.Navbar.showUserInfo(username);
            
            % Switch to detection page
            app.switchToDetectionPage();
            
            fprintf('✅ User %s logged in successfully\n', username);
        end
        
        function onSignupSuccess(app, username)
            % Handle successful signup
            fprintf('✅ User %s registered successfully\n', username);
            % Automatically log them in
            app.onLoginSuccess(username);
        end
        
        function logoutUser(app)
            % Handle user logout
            app.IsLoggedIn = false;
            app.CurrentUser = '';
            app.CurrentPage = 'landing';
            
            % Update navbar
            app.Navbar.showAuthButtons();
            
            % Switch to landing page
            app.switchToLandingPage();
            
            fprintf('✅ User logged out successfully\n');
        end
        
        function switchToDetectionPage(app)
            % Switch to detection page
            app.LandingPage.setVisible('off');
            app.MainPanel.Visible = 'on';
        end
        
        function switchToLandingPage(app)
            % Switch to landing page
            app.MainPanel.Visible = 'off';
            app.LandingPage.setVisible('on');
        end
        
        function saveUserData(app)
            % Save user data to file
            try
                if isa(app.UserData, 'containers.Map')
                    userData = app.UserData;
                    save('user_data.mat', 'userData');
                    fprintf('💾 User data saved successfully\n');
                else
                    fprintf('⚠️ Cannot save: UserData is not a valid containers.Map\n');
                end
            catch ME
                fprintf('⚠️ Error saving user data: %s\n', ME.message);
            end
        end
        
        function updateAllThemes(app)
            % Update all component themes
            app.Navbar.updateTheme();
            app.LandingPage.updateTheme();
            app.LoginPopup.updateTheme();
            app.SignupPopup.updateTheme();
        end
        
        function scrollToSection(app, sectionId)
            % Scroll to section (placeholder)
            fprintf('Scrolling to: %s\n', sectionId);
        end
        
        function showDiseaseDetails(app, diseaseType)
            % Show disease details (placeholder)
            fprintf('Showing details for: %s\n', diseaseType);
        end
        
        function browseImage(app)
            % Browse and select image
            [file, path] = uigetfile({'*.jpg;*.jpeg;*.png;*.bmp', 'Image Files'});
            if file ~= 0
                app.SelectedImagePath = fullfile(path, file);
                img = imread(app.SelectedImagePath);
                imshow(img, 'Parent', app.UIAxes);
                app.ResultLabel.Text = 'Image loaded. Click "Analyze Disease" to proceed.';
            end
        end
        
        function predictDisease(app)
            % Predict disease from image
            if isempty(app.SelectedImagePath)
                app.ResultLabel.Text = 'Please select an image first.';
                return;
            end
            
            app.ResultLabel.Text = 'Analyzing image... Please wait.';
            drawnow;
            
            % Simulate prediction (replace with actual model inference)
            pause(2);
            
            diseases = {'High_Severity', 'Medium_Severity', 'Low_Severity'};
            confidence = 0.85 + 0.1 * rand();
            predicted = diseases{randi(3)};
            
            app.ResultLabel.Text = sprintf('🎯 Detected: %s (%.1f%% confidence)', ...
                predicted, confidence * 100);
            
            app.LastPrediction = struct('disease', predicted, 'confidence', confidence);
        end
        
        function closeApp(app)
            % Close application
            app.saveUserData();
            delete(app.UIFigure);
        end
    end
end
