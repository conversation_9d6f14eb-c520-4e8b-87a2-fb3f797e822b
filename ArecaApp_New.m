classdef ArecaApp_New < matlab.apps.AppBase
    % ArecaApp_New - Modern Arecanut Disease Detection Application
    % Features organized components, landing page, and popup authentication
    
    properties (Access = public)
        UIFigure                matlab.ui.Figure
        
        % New organized components
        Config                  % App configuration
        Navbar                  % Navigation bar component
        LandingPage             % Landing page component
        LoginPopup              % Login popup component
        SignupPopup             % Signup popup component
        DetectionPage           % Detection page component
        
        % Legacy components (for backward compatibility)
        MainPanel               matlab.ui.container.Panel
        BrowseButton            matlab.ui.control.Button
        PredictButton           matlab.ui.control.Button
        LogoutButton            matlab.ui.control.Button
        UIAxes                  matlab.ui.control.UIAxes
        ResultLabel             matlab.ui.control.Label
        TitleLabel              matlab.ui.control.Label
        ControlPanel            matlab.ui.container.Panel
        
        % Application state
        CurrentUser             % Current logged-in user
        IsLoggedIn              % Login status
        CurrentPage             % Current active page
    end
    
    properties (Access = private)
        % Database and authentication
        DatabaseManager        % Unified database manager (MongoDB + local)
        ModelData              % AI model data

        % UI State
        SelectedImagePath      % Currently selected image
        LastPrediction         % Last prediction result

        % Callbacks structure
        AppCallbacks           % Application callback functions
    end
    
    methods (Access = public)
        function app = ArecaApp_New()
            % Constructor - Initialize the application
            
            % Add component paths
            addpath('components');
            addpath('pages');
            addpath('utils');
            addpath('data');
            
            % Initialize configuration
            app.Config = AppConfig();
            
            % Initialize application state
            app.IsLoggedIn = false;
            app.CurrentPage = 'landing';
            app.CurrentUser = '';
            
            % Create UI components
            app.createComponents();
            
            % Setup callbacks
            app.setupCallbacks();
            
            % Initialize database manager (MongoDB + local storage)
            app.initializeDatabase();
            
            % Load AI model
            app.loadAIModel();
            
            % Show the app
            app.UIFigure.Visible = 'on';
            
            fprintf('🌿 Arecanut Disease Detection Platform v2.0 Loaded Successfully!\n');
            fprintf('✨ Features: Landing Page, Popup Auth, Disease Gallery, Treatment Recommendations\n');
            fprintf('🖥️ Application opened in FULL SCREEN mode\n');
            fprintf('⌨️ Keyboard shortcuts available:\n');
            fprintf('   • ESC - Close application\n');
            fprintf('   • F11 - Toggle fullscreen/windowed mode\n');
            fprintf('   • Ctrl+Q - Quit application\n');
        end
        
        function createComponents(app)
            % Create main UI components
            
            % Create main figure in full screen
            app.UIFigure = uifigure('Visible', 'off');

            % Get screen size for full screen
            screenSize = get(0, 'ScreenSize');
            app.UIFigure.Position = [1 1 screenSize(3) screenSize(4)];

            app.UIFigure.Name = app.Config.APP_NAME;
            app.UIFigure.Resize = 'on';  % Allow resizing
            app.UIFigure.WindowState = 'maximized';  % Start maximized
            app.UIFigure.CloseRequestFcn = @(~,~) app.closeApp();

            % Add keyboard shortcuts for better control
            app.UIFigure.KeyPressFcn = @(src, event) app.handleKeyPress(src, event);
            
            % Create main container
            mainContainer = uipanel(app.UIFigure, ...
                'Position', [0 0 1200 800], ...
                'BackgroundColor', app.Config.getCurrentTheme().background, ...
                'BorderType', 'none');
            
            % Initialize callback structure
            app.setupCallbacks();
            
            % Create navbar
            app.Navbar = Navbar(mainContainer, app.Config, app.AppCallbacks);
            
            % Create landing page
            app.LandingPage = LandingPage(mainContainer, app.Config, app.AppCallbacks);
            
            % Create authentication popups
            app.LoginPopup = LoginPopup(app.UIFigure, app.Config, app.AppCallbacks);
            app.SignupPopup = SignupPopup(app.UIFigure, app.Config, app.AppCallbacks);
            
            % Create detection page (initially hidden)
            app.createDetectionPage(mainContainer);
        end
        
        function setupCallbacks(app)
            % Setup application callback functions
            app.AppCallbacks = struct();
            
            % Authentication callbacks
            app.AppCallbacks.showLoginPopup = @() app.showLoginPopup();
            app.AppCallbacks.showSignupPopup = @() app.showSignupPopup();
            app.AppCallbacks.authenticate = @(username, password) app.authenticateUser(username, password);
            app.AppCallbacks.register = @(userData) app.registerUser(userData);
            app.AppCallbacks.onLoginSuccess = @(username) app.onLoginSuccess(username);
            app.AppCallbacks.onSignupSuccess = @(username) app.onSignupSuccess(username);
            app.AppCallbacks.logout = @() app.logoutUser();
            
            % Navigation callbacks
            app.AppCallbacks.scrollToSection = @(sectionId) app.scrollToSection(sectionId);
            app.AppCallbacks.showDiseaseDetails = @(diseaseType) app.showDiseaseDetails(diseaseType);
            
            % Theme callbacks
            app.AppCallbacks.updateTheme = @() app.updateAllThemes();
        end
        
        function createDetectionPage(app, parent)
            % Create detection page (simplified version of original)
            theme = app.Config.getCurrentTheme();
            
            app.MainPanel = uipanel(parent, ...
                'Position', [0 0 1200 750], ...
                'BackgroundColor', theme.background, ...
                'BorderType', 'none', ...
                'Visible', 'off');
            
            % Control panel
            app.ControlPanel = uipanel(app.MainPanel, ...
                'Position', [50 50 500 650], ...
                'BackgroundColor', theme.surface, ...
                'BorderType', 'line', ...
                'BorderWidth', 1, ...
                'HighlightColor', theme.border);
            
            % Title
            app.TitleLabel = uilabel(app.ControlPanel, ...
                'Text', '🌿 AI Disease Detection Dashboard', ...
                'FontSize', 18, 'FontWeight', 'bold', ...
                'FontColor', theme.text_primary, ...
                'Position', [20 600 460 30]);
            
            % Browse button
            app.BrowseButton = uibutton(app.ControlPanel, 'push', ...
                'Text', '📁 Select Image', ...
                'FontSize', 14, 'FontWeight', 'bold', ...
                'FontColor', [1 1 1], ...
                'BackgroundColor', theme.primary, ...
                'Position', [20 550 200 40], ...
                'ButtonPushedFcn', @(~,~) app.browseImage());
            
            % Predict button
            app.PredictButton = uibutton(app.ControlPanel, 'push', ...
                'Text', '🤖 Analyze Disease', ...
                'FontSize', 14, 'FontWeight', 'bold', ...
                'FontColor', [1 1 1], ...
                'BackgroundColor', theme.accent, ...
                'Position', [240 550 200 40], ...
                'ButtonPushedFcn', @(~,~) app.predictDisease());
            
            % Result label
            app.ResultLabel = uilabel(app.ControlPanel, ...
                'Text', 'Upload an image to get started...', ...
                'FontSize', 12, ...
                'FontColor', theme.text_secondary, ...
                'Position', [20 500 460 30]);
            
            % Logout button
            app.LogoutButton = uibutton(app.ControlPanel, 'push', ...
                'Text', '🚪 Logout', ...
                'FontSize', 12, ...
                'FontColor', [1 1 1], ...
                'BackgroundColor', [0.8 0.3 0.3], ...
                'Position', [20 20 100 30], ...
                'ButtonPushedFcn', @(~,~) app.logoutUser());
            
            % Image display axes
            app.UIAxes = uiaxes(app.MainPanel, ...
                'Position', [600 100 550 550], ...
                'BackgroundColor', theme.surface);
            app.UIAxes.Title.String = 'Uploaded Image';
            app.UIAxes.XTick = [];
            app.UIAxes.YTick = [];
        end
        
        function initializeDatabase(app)
            % Initialize database manager with MongoDB and local storage
            try
                fprintf('🗄️ Initializing database system...\n');

                % Create database manager (tries MongoDB first, falls back to local)
                app.DatabaseManager = DatabaseManager();

                fprintf('✅ Database system initialized successfully\n');

                % Display database statistics
                stats = app.DatabaseManager.getDatabaseStats();
                if isfield(stats, 'mongodb_available') && stats.mongodb_available
                    fprintf('🎯 Using MongoDB as primary storage\n');
                else
                    fprintf('🎯 Using local storage as primary storage\n');
                end

            catch ME
                fprintf('❌ Database initialization error: %s\n', ME.message);
                fprintf('   Creating fallback local storage...\n');

                % Create minimal fallback
                try
                    app.DatabaseManager = DatabaseManager();
                catch
                    fprintf('❌ Critical: Cannot initialize any storage system\n');
                end
            end
        end
        
        function loadAIModel(app)
            % Load AI model
            try
                if exist('resnet50_arecanut_model.mat', 'file')
                    load('resnet50_arecanut_model.mat', 'net');
                    app.ModelData = net;
                    fprintf('✅ AI model loaded successfully\n');
                else
                    fprintf('⚠️ AI model file not found\n');
                    app.ModelData = [];
                end
            catch ME
                fprintf('⚠️ Model loading warning: %s\n', ME.message);
                app.ModelData = [];
            end
        end
        
        function showLoginPopup(app)
            % Show login popup
            app.LoginPopup.show();
        end
        
        function showSignupPopup(app)
            % Show signup popup
            app.SignupPopup.show();
        end
        
        function success = authenticateUser(app, username, password)
            % Authenticate user credentials using database manager
            success = false;

            try
                if isempty(app.DatabaseManager)
                    fprintf('⚠️ Database manager not initialized, reinitializing...\n');
                    app.initializeDatabase();
                end

                % Use database manager for authentication
                success = app.DatabaseManager.authenticateUser(username, password);

            catch ME
                fprintf('❌ Authentication error: %s\n', ME.message);
                success = false;
            end
        end
        
        function success = registerUser(app, userData)
            % Register new user using database manager
            success = false;

            try
                if isempty(app.DatabaseManager)
                    fprintf('⚠️ Database manager not initialized, reinitializing...\n');
                    app.initializeDatabase();
                end

                % Set default password for demo (in production, this would be properly hashed)
                userData.password = 'demo123';

                % Use database manager for registration
                success = app.DatabaseManager.insertUser(userData);

            catch ME
                fprintf('❌ Registration error: %s\n', ME.message);
                success = false;
            end
        end
        
        function onLoginSuccess(app, username)
            % Handle successful login
            app.IsLoggedIn = true;
            app.CurrentUser = username;
            app.CurrentPage = 'detection';
            
            % Update navbar
            app.Navbar.showUserInfo(username);
            
            % Switch to detection page
            app.switchToDetectionPage();
            
            fprintf('✅ User %s logged in successfully\n', username);
        end
        
        function onSignupSuccess(app, username)
            % Handle successful signup
            fprintf('✅ User %s registered successfully\n', username);
            % Automatically log them in
            app.onLoginSuccess(username);
        end
        
        function logoutUser(app)
            % Handle user logout
            app.IsLoggedIn = false;
            app.CurrentUser = '';
            app.CurrentPage = 'landing';
            
            % Update navbar
            app.Navbar.showAuthButtons();
            
            % Switch to landing page
            app.switchToLandingPage();
            
            fprintf('✅ User logged out successfully\n');
        end
        
        function switchToDetectionPage(app)
            % Switch to detection page
            app.LandingPage.setVisible('off');
            app.MainPanel.Visible = 'on';
        end
        
        function switchToLandingPage(app)
            % Switch to landing page
            app.MainPanel.Visible = 'off';
            app.LandingPage.setVisible('on');
        end
        
        function showDatabaseStats(app)
            % Show database statistics
            try
                if ~isempty(app.DatabaseManager)
                    stats = app.DatabaseManager.getDatabaseStats();
                    fprintf('\n📊 DATABASE STATISTICS:\n');
                    fprintf('   Primary Storage: %s\n', stats.primary_storage);
                    if stats.mongodb_available
                        fprintf('   MongoDB Available: Yes\n');
                    else
                        fprintf('   MongoDB Available: No\n');
                    end
                    if isfield(stats, 'local_storage')
                        fprintf('   Local Users: %d\n', stats.local_storage.users);
                        fprintf('   Local Detections: %d\n', stats.local_storage.detections);
                        fprintf('   Local Sessions: %d\n', stats.local_storage.sessions);
                    end
                    fprintf('\n');
                end
            catch ME
                fprintf('⚠️ Error getting database stats: %s\n', ME.message);
            end
        end
        
        function updateAllThemes(app)
            % Update all component themes
            app.Navbar.updateTheme();
            app.LandingPage.updateTheme();
            app.LoginPopup.updateTheme();
            app.SignupPopup.updateTheme();
        end
        
        function scrollToSection(app, sectionId)
            % Scroll to section (placeholder)
            fprintf('Scrolling to: %s\n', sectionId);
        end
        
        function showDiseaseDetails(app, diseaseType)
            % Show disease details (placeholder)
            fprintf('Showing details for: %s\n', diseaseType);
        end
        
        function browseImage(app)
            % Browse and select image
            [file, path] = uigetfile({'*.jpg;*.jpeg;*.png;*.bmp', 'Image Files'});
            if file ~= 0
                app.SelectedImagePath = fullfile(path, file);
                img = imread(app.SelectedImagePath);
                imshow(img, 'Parent', app.UIAxes);
                app.ResultLabel.Text = 'Image loaded. Click "Analyze Disease" to proceed.';
            end
        end
        
        function predictDisease(app)
            % Predict disease from image and save to database
            if isempty(app.SelectedImagePath)
                app.ResultLabel.Text = 'Please select an image first.';
                return;
            end

            app.ResultLabel.Text = 'Analyzing image... Please wait.';
            drawnow;

            % Simulate prediction (replace with actual model inference)
            pause(2);

            diseases = {'High_Severity', 'Medium_Severity', 'Low_Severity'};
            confidence = 0.85 + 0.1 * rand();
            predicted = diseases{randi(3)};

            % Create detection result
            detectionResult = struct();
            detectionResult.username = app.CurrentUser;
            detectionResult.disease_type = predicted;
            detectionResult.confidence = confidence;
            detectionResult.image_path = app.SelectedImagePath;
            detectionResult.model_version = '1.0';
            detectionResult.processing_time = 2.0; % seconds

            % Save to database
            try
                if ~isempty(app.DatabaseManager)
                    app.DatabaseManager.insertDetection(detectionResult);
                end
            catch ME
                fprintf('⚠️ Could not save detection result: %s\n', ME.message);
            end

            % Update UI
            app.ResultLabel.Text = sprintf('🎯 Detected: %s (%.1f%% confidence)', ...
                predicted, confidence * 100);

            app.LastPrediction = detectionResult;

            % Show treatment recommendations
            app.showTreatmentRecommendations(predicted, confidence);
        end
        
        function showTreatmentRecommendations(app, diseaseType, confidence)
            % Show AI treatment recommendations
            try
                % Get treatment data
                treatment = TreatmentData.getAIRecommendations(diseaseType, confidence, diseaseType);

                % Create treatment display window
                treatmentFig = uifigure('Name', 'AI Treatment Recommendations', ...
                    'Position', [200 200 600 500]);

                % Treatment panel
                treatmentPanel = uipanel(treatmentFig, ...
                    'Position', [20 20 560 460], ...
                    'BackgroundColor', [0.98 0.99 1], ...
                    'BorderType', 'line');

                % Title
                uilabel(treatmentPanel, ...
                    'Text', sprintf('💊 Treatment for %s', diseaseType), ...
                    'FontSize', 18, 'FontWeight', 'bold', ...
                    'Position', [20 420 520 30]);

                % Confidence level
                uilabel(treatmentPanel, ...
                    'Text', sprintf('🎯 Confidence: %.1f%% - %s', confidence*100, treatment.reliability), ...
                    'FontSize', 12, ...
                    'Position', [20 390 520 20]);

                % Treatment plan
                yPos = 350;
                if isfield(treatment.treatment_plan, 'immediate_actions')
                    uilabel(treatmentPanel, ...
                        'Text', '🚨 Immediate Actions:', ...
                        'FontSize', 14, 'FontWeight', 'bold', ...
                        'Position', [20 yPos 520 20]);
                    yPos = yPos - 25;

                    actions = treatment.treatment_plan.immediate_actions;
                    for i = 1:min(3, length(actions))
                        uilabel(treatmentPanel, ...
                            'Text', sprintf('• %s', actions{i}), ...
                            'FontSize', 11, ...
                            'Position', [40 yPos 480 15]);
                        yPos = yPos - 20;
                    end
                end

                % Close button
                uibutton(treatmentPanel, 'push', ...
                    'Text', 'Close', ...
                    'Position', [250 20 60 30], ...
                    'ButtonPushedFcn', @(~,~) close(treatmentFig));

            catch ME
                fprintf('⚠️ Error showing treatment recommendations: %s\n', ME.message);
            end
        end

        function handleKeyPress(app, ~, event)
            % Handle keyboard shortcuts for better control
            try
                % Check for Escape key to close app
                if strcmp(event.Key, 'escape')
                    fprintf('🔑 Escape key pressed - closing application...\n');
                    app.closeApp();
                % Check for F11 to toggle fullscreen
                elseif strcmp(event.Key, 'f11')
                    if strcmp(app.UIFigure.WindowState, 'maximized')
                        app.UIFigure.WindowState = 'normal';
                        fprintf('🔑 F11 pressed - window restored\n');
                    else
                        app.UIFigure.WindowState = 'maximized';
                        fprintf('🔑 F11 pressed - window maximized\n');
                    end
                % Check for Ctrl+Q to quit
                elseif strcmp(event.Key, 'q') && any(strcmp(event.Modifier, 'control'))
                    fprintf('🔑 Ctrl+Q pressed - closing application...\n');
                    app.closeApp();
                end
            catch ME
                fprintf('❌ Error handling key press: %s\n', ME.message);
            end
        end

        function closeApp(app)
            % Close application and cleanup
            try
                fprintf('🔄 Closing Arecanut Disease Detection Platform...\n');

                % Disconnect database
                if ~isempty(app.DatabaseManager)
                    app.DatabaseManager.disconnect();
                end

                % Close figure safely
                if ~isempty(app.UIFigure) && isvalid(app.UIFigure)
                    delete(app.UIFigure);
                end

                fprintf('✅ Application closed successfully\n');
                fprintf('💡 Keyboard shortcuts available:\n');
                fprintf('   • ESC - Close application\n');
                fprintf('   • F11 - Toggle fullscreen\n');
                fprintf('   • Ctrl+Q - Quit application\n\n');

            catch ME
                fprintf('❌ Error closing application: %s\n', ME.message);
            end
        end
    end
end
