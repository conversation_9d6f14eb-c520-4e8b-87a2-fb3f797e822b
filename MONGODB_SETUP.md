# MongoDB Setup for Arecanut Disease Detection App

## Prerequisites

### 1. Install MongoDB
Download and install MongoDB Community Server from: https://www.mongodb.com/try/download/community

### 2. Install MongoDB Java Driver for MATLAB
You need to add the MongoDB Java driver to MATLAB's classpath.

#### Option A: Download JAR file
1. Download `mongodb-driver-sync-4.11.1.jar` from Maven Central
2. Place it in your MATLAB project folder
3. Add to MATLAB classpath:
```matlab
javaaddpath('mongodb-driver-sync-4.11.1.jar');
```

#### Option B: Use Maven (if available)
```xml
<dependency>
    <groupId>org.mongodb</groupId>
    <artifactId>mongodb-driver-sync</artifactId>
    <version>4.11.1</version>
</dependency>
```

## MongoDB Setup Steps

### 1. Start MongoDB Service
**Windows:**
```cmd
net start MongoDB
```

**macOS/Linux:**
```bash
sudo systemctl start mongod
# or
brew services start mongodb-community
```

### 2. Verify MongoDB is Running
Open command prompt/terminal and run:
```bash
mongosh
```

You should see MongoDB shell connect successfully.

### 3. Create Database and Collection
In MongoDB shell:
```javascript
use arecanut_app
db.createCollection("users")
```

## MATLAB Configuration

### 1. Add MongoDB Driver to MATLAB
```matlab
% Add MongoDB Java driver to classpath
javaaddpath('path/to/mongodb-driver-sync-4.11.1.jar');

% Verify import works
import com.mongodb.client.MongoClients
```

### 2. Test Connection
```matlab
% Test MongoDB connection
try
    import com.mongodb.client.MongoClients
    uri = "mongodb://localhost:27017";
    client = MongoClients.create(uri);
    db = client.getDatabase("arecanut_app");
    fprintf('✅ MongoDB connection successful!\n');
catch ME
    fprintf('❌ MongoDB connection failed: %s\n', ME.message);
end
```

## Database Schema

### Users Collection
```json
{
  "_id": ObjectId("..."),
  "username": "string",
  "password": "string (hashed)",
  "name": "string",
  "phone": "string",
  "gmail": "string",
  "created_at": Date
}
```

## Troubleshooting

### Common Issues:

1. **"MongoDB connection failed"**
   - Ensure MongoDB service is running
   - Check if port 27017 is available
   - Verify MongoDB is installed correctly

2. **"Cannot find or import 'com.mongodb.client.MongoClients'"**
   - MongoDB Java driver not in classpath
   - Download and add the JAR file to MATLAB

3. **"Access denied" or permission errors**
   - Run MATLAB as administrator (Windows)
   - Check MongoDB service permissions

4. **Connection timeout**
   - Check firewall settings
   - Verify MongoDB is listening on localhost:27017

### Verification Commands:

**Check MongoDB Status:**
```bash
# Windows
sc query MongoDB

# macOS/Linux
sudo systemctl status mongod
```

**Check Database:**
```javascript
// In mongosh
use arecanut_app
show collections
db.users.find()
```

## Security Notes

- Default setup uses no authentication (development only)
- For production, enable MongoDB authentication
- Use environment variables for connection strings
- Implement proper error handling

## Next Steps

1. Start MongoDB service
2. Add MongoDB driver to MATLAB classpath
3. Run the ArecaApp
4. Test user registration and login

The app will now store all user data in MongoDB instead of MAT files!
