# 🌿 Arecanut Disease Detection - Quick Start Guide

## 🚀 One-Command Setup and Run

### **Option 1: Automatic Setup (Recommended)**
```bash
python setup_and_run.py
```

This will automatically:
- ✅ Check all requirements
- ✅ Install Python dependencies
- ✅ Install Node.js dependencies  
- ✅ Start API server (localhost:5000)
- ✅ Start TypeScript frontend (localhost:8080)
- ✅ Initialize MATLAB model
- ✅ Open browser automatically

---

## 🔧 Manual Setup (If needed)

### **Prerequisites**
1. **Python 3.8+** with MATLAB Engine
2. **Node.js 16+** 
3. **MATLAB** with Deep Learning Toolbox
4. **Files**: `resnet50_arecanut_model.mat` in root directory

### **Step 1: Install Python Dependencies**
```bash
pip install -r requirements.txt
pip install matlabengine
```

### **Step 2: Install Node.js Dependencies**
```bash
cd areca-nut
npm install
cd ..
```

### **Step 3: Start API Server**
```bash
python prediction_api.py
```
Server will start on `http://localhost:5000`

### **Step 4: Start Frontend (New Terminal)**
```bash
cd areca-nut
npm run dev
```
Frontend will start on `http://localhost:8080`

### **Step 5: Initialize MATLAB Model**
```bash
curl -X POST http://localhost:5000/initialize
```

---

## 🎯 How to Use

### **1. Access the Application**
- Open browser: `http://localhost:8080`
- Register new account or login

### **2. Disease Detection**
- Go to Dashboard
- Upload arecanut leaf image
- Click "Analyze Image"
- Get AI-powered disease prediction

### **3. API Endpoints**
- `GET /health` - Check API status
- `POST /initialize` - Initialize MATLAB model
- `POST /predict` - Predict disease from image
- `GET /model-info` - Get model information

---

## 🔍 Troubleshooting

### **MATLAB Engine Issues**
```bash
# Install MATLAB Engine for Python
cd "C:\Program Files\MATLAB\R2023b\extern\engines\python"
python setup.py install
```

### **API Server Not Starting**
- Check if port 5000 is available
- Ensure MATLAB is installed
- Verify `resnet50_arecanut_model.mat` exists

### **Frontend Issues**
- Check if port 8080 is available
- Run `npm install` in areca-nut folder
- Clear browser cache

### **Prediction Errors**
- Ensure API server is running
- Check image format (JPG, PNG supported)
- Verify MATLAB model is initialized

---

## 📊 System Architecture

```
TypeScript Frontend (React)
         ↓ HTTP API
Python Flask Server
         ↓ MATLAB Engine
MATLAB ResNet-50 Model (.mat)
```

### **Data Flow**
1. User uploads image in React frontend
2. Image sent as base64 to Python API
3. Python converts and sends to MATLAB
4. MATLAB model predicts disease
5. Result formatted and returned to frontend

---

## 🎉 Success Indicators

✅ **API Server**: `http://localhost:5000/health` returns status  
✅ **Frontend**: `http://localhost:8080` loads React app  
✅ **MATLAB**: Model initialization successful  
✅ **Prediction**: Image analysis returns disease classification  

---

## 📞 Support

If you encounter issues:
1. Check all prerequisites are installed
2. Verify file paths and permissions
3. Check console logs for error messages
4. Ensure MATLAB license is valid

**Ready to detect arecanut diseases with AI! 🌿🤖**
