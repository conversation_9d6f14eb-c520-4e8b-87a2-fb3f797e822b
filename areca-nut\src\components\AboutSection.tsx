import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Leaf, MapPin, Calendar, Coins } from "lucide-react";

export const AboutSection = () => {
  const facts = [
    {
      icon: MapPin,
      title: "Origin & Distribution",
      description: "Native to Southeast Asia, widely cultivated in India, Bangladesh, Myanmar, and parts of East Africa."
    },
    {
      icon: Calendar,
      title: "Growth Cycle",
      description: "Takes 6-8 years to mature. Productive lifespan of 40-60 years with proper care and management."
    },
    {
      icon: Coins,
      title: "Economic Importance",
      description: "Major cash crop providing livelihood to millions. Used in traditional medicine and cultural practices."
    },
    {
      icon: Leaf,
      title: "Plant Characteristics",
      description: "Tall palm tree reaching 12-20 meters. Produces orange or red fruits containing the valuable nut."
    }
  ];

  return (
    <section id="about" className="py-20 bg-gradient-earth">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-nature-primary mb-6">
            About Arecanut
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Arecanut (Areca catechu), also known as betel nut, is an important agricultural crop 
            with significant economic and cultural value across Asia and other tropical regions.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {facts.map((fact, index) => (
            <Card key={index} className="shadow-card-nature hover:shadow-nature transition-shadow duration-300">
              <CardHeader className="text-center">
                <fact.icon className="h-12 w-12 text-nature-primary mx-auto mb-4" />
                <CardTitle className="text-lg">{fact.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground text-center">{fact.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div>
            <h3 className="text-3xl font-bold text-nature-primary mb-6">
              Cultivation Requirements
            </h3>
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-nature-primary rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <h4 className="font-semibold mb-1">Climate</h4>
                  <p className="text-muted-foreground">Tropical and subtropical regions with high humidity and rainfall (1500-3000mm annually)</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-nature-primary rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <h4 className="font-semibold mb-1">Soil</h4>
                  <p className="text-muted-foreground">Well-drained, fertile soils with pH 6.0-7.5. Sandy loam to clay loam preferred</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-nature-primary rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <h4 className="font-semibold mb-1">Temperature</h4>
                  <p className="text-muted-foreground">Optimal range 20-32°C. Cannot tolerate frost or extreme temperature fluctuations</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-nature-primary rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <h4 className="font-semibold mb-1">Spacing</h4>
                  <p className="text-muted-foreground">Typically planted 2.7m x 2.7m apart, allowing for proper air circulation and light penetration</p>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-3xl font-bold text-nature-primary mb-6">
              Economic & Cultural Value
            </h3>
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-accent rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <h4 className="font-semibold mb-1">Traditional Use</h4>
                  <p className="text-muted-foreground">Central to betel chewing culture, religious ceremonies, and traditional medicine systems</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-accent rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <h4 className="font-semibold mb-1">Commercial Products</h4>
                  <p className="text-muted-foreground">Processed into supari, used in pharmaceutical preparations, and traditional formulations</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-accent rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <h4 className="font-semibold mb-1">Export Value</h4>
                  <p className="text-muted-foreground">Significant export commodity contributing to foreign exchange earnings</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-accent rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <h4 className="font-semibold mb-1">Employment</h4>
                  <p className="text-muted-foreground">Provides direct and indirect employment to millions in cultivation, processing, and trade</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};