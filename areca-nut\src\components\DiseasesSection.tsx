import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { AlertTriangle, Bug, Droplets, Shield } from "lucide-react";

export const DiseasesSection = () => {
  const diseases = [
    {
      name: "Fruit Rot",
      severity: "High",
      pathogen: "Phytophthora palmivora",
      symptoms: [
        "Water-soaked lesions on young fruits",
        "Brown to black discoloration",
        "Fruit drop and yield loss",
        "White fungal growth in humid conditions"
      ],
      management: [
        "Improve drainage and air circulation",
        "Remove infected fruits immediately",
        "Apply copper-based fungicides",
        "Use Bordeaux mixture as preventive spray"
      ]
    },
    {
      name: "Bud Rot",
      severity: "Critical",
      pathogen: "Phytophthora palmivora",
      symptoms: [
        "Rotting of the growing point",
        "Foul smell from infected bud",
        "Yellowing and wilting of leaves",
        "Death of the palm if untreated"
      ],
      management: [
        "Early detection and removal of infected tissue",
        "Apply fungicide paste to cut areas",
        "Improve soil drainage",
        "Avoid mechanical injuries to the bud"
      ]
    },
    {
      name: "St<PERSON> Bleeding",
      severity: "Medium",
      pathogen: "Ceratocystis paradoxa",
      symptoms: [
        "Dark reddish-brown exudate from stem",
        "Internal tissue discoloration",
        "Reduced vigor and growth",
        "Gradual decline in productivity"
      ],
      management: [
        "Clean affected areas and apply fungicide",
        "Remove severely affected palms",
        "Improve nutrition and water management",
        "Use resistant varieties where available"
      ]
    },
    {
      name: "Leaf Spot",
      severity: "Low",
      pathogen: "Pestalotiopsis sp.",
      symptoms: [
        "Small brown spots with yellow halos",
        "Spots may coalesce on severe infection",
        "Premature leaf yellowing",
        "Reduced photosynthetic capacity"
      ],
      management: [
        "Remove infected leaves",
        "Apply copper-based fungicides",
        "Maintain proper spacing for air circulation",
        "Balanced nutrition to improve resistance"
      ]
    }
  ];

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case "Critical": return "destructive";
      case "High": return "default";
      case "Medium": return "secondary";
      case "Low": return "outline";
      default: return "outline";
    }
  };

  return (
    <section id="diseases" className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <div className="flex items-center justify-center gap-3 mb-6">
            <Bug className="h-10 w-10 text-destructive" />
            <h2 className="text-4xl md:text-5xl font-bold text-nature-primary">
              Common Diseases
            </h2>
          </div>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Understanding and managing diseases is crucial for successful arecanut cultivation. 
            Early detection and proper management can prevent significant crop losses.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-8">
          {diseases.map((disease, index) => (
            <Card key={index} className="shadow-card-nature hover:shadow-nature transition-all duration-300">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-xl text-nature-primary mb-2">
                      {disease.name}
                    </CardTitle>
                    <p className="text-sm text-muted-foreground mb-3">
                      <span className="font-medium">Pathogen:</span> {disease.pathogen}
                    </p>
                  </div>
                  <Badge variant={getSeverityColor(disease.severity)}>
                    {disease.severity}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <div className="flex items-center gap-2 mb-3">
                    <AlertTriangle className="h-4 w-4 text-destructive" />
                    <h4 className="font-semibold">Symptoms</h4>
                  </div>
                  <ul className="space-y-1">
                    {disease.symptoms.map((symptom, idx) => (
                      <li key={idx} className="text-sm text-muted-foreground flex items-start gap-2">
                        <div className="w-1.5 h-1.5 bg-destructive rounded-full mt-2 flex-shrink-0"></div>
                        {symptom}
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <div className="flex items-center gap-2 mb-3">
                    <Shield className="h-4 w-4 text-nature-primary" />
                    <h4 className="font-semibold">Management</h4>
                  </div>
                  <ul className="space-y-1">
                    {disease.management.map((practice, idx) => (
                      <li key={idx} className="text-sm text-muted-foreground flex items-start gap-2">
                        <div className="w-1.5 h-1.5 bg-nature-primary rounded-full mt-2 flex-shrink-0"></div>
                        {practice}
                      </li>
                    ))}
                  </ul>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="mt-16 bg-muted/50 rounded-lg p-8">
          <div className="flex items-start gap-4">
            <Droplets className="h-8 w-8 text-nature-primary flex-shrink-0 mt-1" />
            <div>
              <h3 className="text-2xl font-bold text-nature-primary mb-4">
                General Disease Prevention
              </h3>
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold mb-2">Cultural Practices</h4>
                  <ul className="space-y-1 text-muted-foreground">
                    <li>• Maintain proper spacing between palms</li>
                    <li>• Ensure adequate drainage</li>
                    <li>• Regular removal of dead and diseased material</li>
                    <li>• Balanced fertilization program</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Chemical Control</h4>
                  <ul className="space-y-1 text-muted-foreground">
                    <li>• Preventive fungicide applications</li>
                    <li>• Use of copper-based compounds</li>
                    <li>• Systematic application schedule</li>
                    <li>• Rotation of different fungicide groups</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};