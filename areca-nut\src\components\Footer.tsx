import { Leaf, Mail, Phone, MapPin } from "lucide-react";

export const Footer = () => {
  return (
    <footer className="bg-nature-primary text-primary-foreground py-12">
      <div className="container mx-auto px-4">
        <div className="grid md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="col-span-1">
            <div className="flex items-center gap-2 mb-4">
              <Leaf className="h-8 w-8 text-nature-secondary" />
              <h3 className="text-2xl font-bold">ArecaNut Info</h3>
            </div>
            <p className="text-primary-foreground/80 leading-relaxed">
              Comprehensive resource for arecanut cultivation, disease management, and agricultural best practices.
            </p>
          </div>

          {/* Quick Links */}
          <div className="col-span-1">
            <h4 className="text-lg font-semibold mb-4">Quick Links</h4>
            <ul className="space-y-2">
              <li>
                <button 
                  onClick={() => document.getElementById('about')?.scrollIntoView({ behavior: 'smooth' })}
                  className="text-primary-foreground/80 hover:text-nature-secondary transition-colors"
                >
                  About Arecanut
                </button>
              </li>
              <li>
                <button 
                  onClick={() => document.getElementById('diseases')?.scrollIntoView({ behavior: 'smooth' })}
                  className="text-primary-foreground/80 hover:text-nature-secondary transition-colors"
                >
                  Disease Management
                </button>
              </li>
              <li>
                <button 
                  onClick={() => document.getElementById('weeds')?.scrollIntoView({ behavior: 'smooth' })}
                  className="text-primary-foreground/80 hover:text-nature-secondary transition-colors"
                >
                  Weed Control
                </button>
              </li>
            </ul>
          </div>

          {/* Resources */}
          <div className="col-span-1">
            <h4 className="text-lg font-semibold mb-4">Resources</h4>
            <ul className="space-y-2 text-primary-foreground/80">
              <li>Cultivation Guide</li>
              <li>Pest Identification</li>
              <li>Market Information</li>
              <li>Research Papers</li>
            </ul>
          </div>

          {/* Contact Info */}
          <div className="col-span-1">
            <h4 className="text-lg font-semibold mb-4">Contact</h4>
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <Mail className="h-4 w-4 text-nature-secondary" />
                <span className="text-primary-foreground/80"><EMAIL></span>
              </div>
              <div className="flex items-center gap-3">
                <Phone className="h-4 w-4 text-nature-secondary" />
                <span className="text-primary-foreground/80">+91 9876543210</span>
              </div>
              <div className="flex items-center gap-3">
                <MapPin className="h-4 w-4 text-nature-secondary" />
                <span className="text-primary-foreground/80">Agricultural Research Center</span>
              </div>
            </div>
          </div>
        </div>

        <hr className="my-8 border-primary-foreground/20" />

        <div className="flex flex-col md:flex-row justify-between items-center">
          <p className="text-primary-foreground/60 text-sm">
            © 2024 ArecaNut Info. All rights reserved.
          </p>
          <p className="text-primary-foreground/60 text-sm mt-2 md:mt-0">
            Promoting sustainable arecanut cultivation
          </p>
        </div>
      </div>
    </footer>
  );
};