import { Button } from "@/components/ui/button";
import { <PERSON>, Bug, Sprout, Menu, LogIn } from "lucide-react";
import { useState } from "react";
import { useNavigate } from "react-router-dom";

export const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const navigate = useNavigate();

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
    setIsMenuOpen(false);
  };

  const handleLoginClick = () => {
    navigate('/auth');
    setIsMenuOpen(false);
  };

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-background/95 backdrop-blur-sm border-b">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Leaf className="h-8 w-8 text-nature-primary" />
            <h1 className="text-2xl font-bold text-nature-primary">ArecaNut Info</h1>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center gap-6">
            <Button
              variant="ghost"
              onClick={() => scrollToSection('about')}
              className="flex items-center gap-2"
            >
              <Leaf className="h-4 w-4" />
              About Arecanut
            </Button>
            <Button
              variant="ghost"
              onClick={() => scrollToSection('diseases')}
              className="flex items-center gap-2"
            >
              <Bug className="h-4 w-4" />
              Diseases
            </Button>
            <Button
              variant="ghost"
              onClick={() => scrollToSection('weeds')}
              className="flex items-center gap-2"
            >
              <Sprout className="h-4 w-4" />
              Weed Management
            </Button>
            <Button
              variant="outline"
              onClick={handleLoginClick}
              className="flex items-center gap-2"
            >
              <LogIn className="h-4 w-4" />
              Login
            </Button>
          </nav>

          {/* Mobile Menu Button */}
          <Button
            variant="ghost"
            size="icon"
            className="md:hidden"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <Menu className="h-6 w-6" />
          </Button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <nav className="md:hidden mt-4 pb-4 border-t pt-4">
            <div className="flex flex-col gap-2">
              <Button
                variant="ghost"
                onClick={() => scrollToSection('about')}
                className="justify-start"
              >
                <Leaf className="h-4 w-4 mr-2" />
                About Arecanut
              </Button>
              <Button
                variant="ghost"
                onClick={() => scrollToSection('diseases')}
                className="justify-start"
              >
                <Bug className="h-4 w-4 mr-2" />
                Diseases
              </Button>
              <Button
                variant="ghost"
                onClick={() => scrollToSection('weeds')}
                className="justify-start"
              >
                <Sprout className="h-4 w-4 mr-2" />
                Weed Management
              </Button>
              <Button
                variant="outline"
                onClick={handleLoginClick}
                className="justify-start"
              >
                <LogIn className="h-4 w-4 mr-2" />
                Login
              </Button>
            </div>
          </nav>
        )}
      </div>
    </header>
  );
};