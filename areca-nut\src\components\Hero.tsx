import { Button } from "@/components/ui/button";
import { ArrowDown, Leaf } from "lucide-react";
import heroImage from "@/assets/arecanut-hero.jpg";

export const Hero = () => {
  const scrollToAbout = () => {
    const element = document.getElementById('about');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Image */}
      <div 
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{ backgroundImage: `url(${heroImage})` }}
      />
      <div className="absolute inset-0 bg-gradient-hero" />

      {/* Content */}
      <div className="relative z-10 text-center text-white px-4 max-w-4xl mx-auto">
        <div className="flex items-center justify-center gap-3 mb-6">
          <Leaf className="h-12 w-12 text-nature-secondary" />
          <h1 className="text-5xl md:text-7xl font-bold">
            Arecanut
          </h1>
        </div>
        <p className="text-xl md:text-2xl mb-8 text-white/90 leading-relaxed">
          Complete guide to Arecanut cultivation, disease management, and weed control. 
          Essential information for farmers and agricultural professionals.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button 
            variant="nature" 
            size="lg"
            onClick={scrollToAbout}
            className="text-lg px-8 py-6"
          >
            Learn More
            <ArrowDown className="ml-2 h-5 w-5" />
          </Button>
          <Button 
            variant="outline" 
            size="lg"
            onClick={() => document.getElementById('diseases')?.scrollIntoView({ behavior: 'smooth' })}
            className="text-lg px-8 py-6 bg-white/10 border-white/30 text-white hover:bg-white/20"
          >
            View Diseases
          </Button>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <ArrowDown className="h-6 w-6 text-white/70" />
      </div>
    </section>
  );
};