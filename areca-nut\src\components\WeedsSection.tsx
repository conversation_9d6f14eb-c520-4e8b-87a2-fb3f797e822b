import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Sprout, Scissors, Zap, Leaf, AlertTriangle } from "lucide-react";

export const WeedsSection = () => {
  const weeds = [
    {
      name: "Cyperus rotundus",
      commonName: "Purple Nutsedge",
      type: "Perennial Sedge",
      severity: "High",
      characteristics: [
        "Deep root system with tubers",
        "Triangular stems",
        "Purple-brown flower spikes",
        "Rapid multiplication through tubers"
      ],
      control: [
        "Deep ploughing before planting",
        "2,4-D application at 1.5 kg/ha",
        "Manual removal of tubers",
        "Mulching to suppress growth"
      ]
    },
    {
      name: "Imperata cylindrica",
      commonName: "Cogon Grass",
      type: "Perennial Grass",
      severity: "High",
      characteristics: [
        "Rhizomatous perennial grass",
        "Sharp-edged leaves",
        "White fluffy seed heads",
        "Fire-resistant and aggressive"
      ],
      control: [
        "Glyphosate at 2-3 L/ha",
        "Deep cultivation to break rhizomes",
        "Repeated cutting before seed set",
        "Cover crop establishment"
      ]
    },
    {
      name: "<PERSON><PERSON><PERSON> micrantha",
      commonName: "Mile-a-minute Weed",
      type: "Climbing Vine",
      severity: "Critical",
      characteristics: [
        "Fast-growing climbing vine",
        "Heart-shaped leaves",
        "Smothers other vegetation",
        "Rapid vegetative propagation"
      ],
      control: [
        "Manual removal before flowering",
        "2,4-D + Glyphosate combination",
        "Biological control agents",
        "Regular monitoring and early intervention"
      ]
    },
    {
      name: "Chromolaena odorata",
      commonName: "Siam Weed",
      type: "Perennial Shrub",
      severity: "Medium",
      characteristics: [
        "Fast-growing shrub",
        "Opposite leaves with 3 veins",
        "White flower clusters",
        "Allelopathic effects on crops"
      ],
      control: [
        "Cutting before seed formation",
        "Glyphosate application on regrowth",
        "Biocontrol with Pareuchaetes pseudoinsulata",
        "Mulching to prevent establishment"
      ]
    }
  ];

  const managementStrategies = [
    {
      icon: Scissors,
      title: "Mechanical Control",
      methods: [
        "Hand weeding around young palms",
        "Slashing and cutting of tall weeds",
        "Use of brush cutters for larger areas",
        "Proper timing to prevent seed set"
      ]
    },
    {
      icon: Zap,
      title: "Chemical Control",
      methods: [
        "Pre-emergence herbicides",
        "Post-emergence selective herbicides",
        "Directed spray applications",
        "Tank mix combinations for broad spectrum control"
      ]
    },
    {
      icon: Leaf,
      title: "Cultural Control",
      methods: [
        "Mulching with organic materials",
        "Cover crop establishment",
        "Proper spacing and canopy management",
        "Intercropping with beneficial plants"
      ]
    }
  ];

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case "Critical": return "destructive";
      case "High": return "default";
      case "Medium": return "secondary";
      case "Low": return "outline";
      default: return "outline";
    }
  };

  return (
    <section id="weeds" className="py-20 bg-gradient-earth">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <div className="flex items-center justify-center gap-3 mb-6">
            <Sprout className="h-10 w-10 text-nature-primary" />
            <h2 className="text-4xl md:text-5xl font-bold text-nature-primary">
              Weed Management
            </h2>
          </div>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Effective weed management is essential for arecanut productivity. 
            Weeds compete for nutrients, water, and light, significantly reducing yields if not properly controlled.
          </p>
        </div>

        {/* Common Weeds */}
        <div className="mb-16">
          <h3 className="text-3xl font-bold text-nature-primary mb-8 text-center">
            Major Weed Species
          </h3>
          <div className="grid lg:grid-cols-2 gap-8">
            {weeds.map((weed, index) => (
              <Card key={index} className="shadow-card-nature hover:shadow-nature transition-all duration-300">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle className="text-lg text-nature-primary mb-1">
                        {weed.name}
                      </CardTitle>
                      <p className="text-base font-medium text-muted-foreground mb-2">
                        {weed.commonName}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {weed.type}
                      </p>
                    </div>
                    <Badge variant={getSeverityColor(weed.severity)}>
                      {weed.severity}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-semibold mb-2 flex items-center gap-2">
                      <AlertTriangle className="h-4 w-4 text-nature-secondary" />
                      Characteristics
                    </h4>
                    <ul className="space-y-1">
                      {weed.characteristics.map((char, idx) => (
                        <li key={idx} className="text-sm text-muted-foreground flex items-start gap-2">
                          <div className="w-1.5 h-1.5 bg-nature-secondary rounded-full mt-2 flex-shrink-0"></div>
                          {char}
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div>
                    <h4 className="font-semibold mb-2 flex items-center gap-2">
                      <Sprout className="h-4 w-4 text-nature-primary" />
                      Control Methods
                    </h4>
                    <ul className="space-y-1">
                      {weed.control.map((method, idx) => (
                        <li key={idx} className="text-sm text-muted-foreground flex items-start gap-2">
                          <div className="w-1.5 h-1.5 bg-nature-primary rounded-full mt-2 flex-shrink-0"></div>
                          {method}
                        </li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Management Strategies */}
        <div className="mb-16">
          <h3 className="text-3xl font-bold text-nature-primary mb-8 text-center">
            Integrated Weed Management
          </h3>
          <div className="grid md:grid-cols-3 gap-8">
            {managementStrategies.map((strategy, index) => (
              <Card key={index} className="shadow-card-nature text-center">
                <CardHeader>
                  <strategy.icon className="h-12 w-12 text-nature-primary mx-auto mb-4" />
                  <CardTitle className="text-xl">{strategy.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-left">
                    {strategy.methods.map((method, idx) => (
                      <li key={idx} className="text-sm text-muted-foreground flex items-start gap-2">
                        <div className="w-1.5 h-1.5 bg-nature-primary rounded-full mt-2 flex-shrink-0"></div>
                        {method}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Best Practices */}
        <div className="bg-card rounded-lg p-8 shadow-card-nature">
          <h3 className="text-2xl font-bold text-nature-primary mb-6 text-center">
            Best Practices for Weed Control
          </h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="space-y-2">
              <h4 className="font-semibold text-nature-primary">Prevention</h4>
              <ul className="space-y-1 text-sm text-muted-foreground">
                <li>• Use weed-free planting material</li>
                <li>• Clean equipment between fields</li>
                <li>• Quarantine new plant introductions</li>
                <li>• Monitor field boundaries regularly</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-semibold text-nature-primary">Timing</h4>
              <ul className="space-y-1 text-sm text-muted-foreground">
                <li>• Early intervention is most effective</li>
                <li>• Target weeds before reproduction</li>
                <li>• Schedule based on growth stages</li>
                <li>• Consider weather conditions</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-semibold text-nature-primary">Integration</h4>
              <ul className="space-y-1 text-sm text-muted-foreground">
                <li>• Combine multiple control methods</li>
                <li>• Rotate herbicide modes of action</li>
                <li>• Use biological controls where available</li>
                <li>• Maintain detailed control records</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};