@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 45 20% 98%;
    --foreground: 120 8% 15%;

    --card: 0 0% 100%;
    --card-foreground: 120 8% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 120 8% 15%;

    --primary: 120 60% 25%;
    --primary-foreground: 0 0% 98%;

    --secondary: 120 20% 95%;
    --secondary-foreground: 120 60% 25%;

    --muted: 120 15% 96%;
    --muted-foreground: 120 8% 45%;

    --accent: 45 85% 45%;
    --accent-foreground: 120 8% 15%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    --border: 120 15% 88%;
    --input: 120 15% 88%;
    --ring: 120 60% 25%;

    /* Agricultural theme colors */
    --nature-primary: 120 60% 25%;
    --nature-secondary: 45 85% 45%;
    --earth-brown: 30 25% 35%;
    --leaf-green: 140 45% 40%;
    
    /* Gradients */
    --gradient-nature: linear-gradient(135deg, hsl(var(--nature-primary)), hsl(var(--leaf-green)));
    --gradient-earth: linear-gradient(180deg, hsl(var(--background)), hsl(var(--secondary)));
    --gradient-hero: linear-gradient(135deg, hsl(var(--nature-primary) / 0.9), hsl(var(--accent) / 0.8));
    
    /* Shadows */
    --shadow-nature: 0 10px 30px -10px hsl(var(--nature-primary) / 0.3);
    --shadow-card: 0 4px 12px -2px hsl(var(--nature-primary) / 0.1);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}