// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://mdvdhkkdaekjdxbwryhl.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1kdmRoa2tkYWVramR4YndyeWhsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI2ODcwMjcsImV4cCI6MjA2ODI2MzAyN30.4LuSr00KW0xVLb0oFwAZY1GMcEcxiXxBXJ_T8IMEP3g";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});