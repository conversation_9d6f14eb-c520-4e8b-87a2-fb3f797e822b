import { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { User, Session } from '@supabase/supabase-js';
import { Upload, LogOut, Camera, FileImage } from "lucide-react";

const Dashboard = () => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [analyzing, setAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<string | null>(null);
  const navigate = useNavigate();
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    // Set up auth state listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        setSession(session);
        setUser(session?.user ?? null);
        
        if (!session?.user) {
          navigate('/auth');
        }
      }
    );

    // Check for existing session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      
      if (!session?.user) {
        navigate('/auth');
      }
    });

    return () => subscription.unsubscribe();
  }, [navigate]);

  const handleSignOut = async () => {
    try {
      await supabase.auth.signOut();
      navigate('/auth');
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const handleImageSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedImage(file);
      
      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
      
      // Clear previous analysis
      setAnalysisResult(null);
    }
  };

  const handleAnalyzeImage = async () => {
    if (!selectedImage) {
      toast({
        title: "No image selected",
        description: "Please select an image first",
        variant: "destructive",
      });
      return;
    }

    setAnalyzing(true);

    try {
      // Convert image to base64
      const reader = new FileReader();
      const imageBase64 = await new Promise<string>((resolve, reject) => {
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(selectedImage);
      });

      // Call MATLAB prediction API
      const response = await fetch('http://localhost:5000/predict', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          image: imageBase64
        })
      });

      if (!response.ok) {
        throw new Error(`API Error: ${response.status}`);
      }

      const result = await response.json();

      if (result.status === 'error') {
        throw new Error(result.error);
      }

      // Format the result for display
      const formattedResult = `${result.icon} ${result.disease} detected - Confidence: ${result.confidence}%

Severity: ${result.severity}
${result.description}

Recommendations:
${result.recommendations.map((rec: string, index: number) => `${index + 1}. ${rec}`).join('\n')}`;

      setAnalysisResult(formattedResult);

      toast({
        title: "Analysis Complete",
        description: `${result.disease} detected with ${result.confidence}% confidence`,
      });

    } catch (error: any) {
      console.error('Prediction error:', error);

      // Check if API server is running
      if (error.message.includes('fetch')) {
        toast({
          title: "API Server Not Running",
          description: "Please start the Python prediction server first",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Analysis Failed",
          description: error.message,
          variant: "destructive",
        });
      }
    } finally {
      setAnalyzing(false);
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  if (!user) {
    return <div>Loading...</div>;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-agricultural-50">
      {/* Header */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-agricultural-900">Disease Detection Dashboard</h1>
            <p className="text-muted-foreground">Welcome back, {user.email}</p>
          </div>
          <Button onClick={handleSignOut} variant="outline" size="sm">
            <LogOut className="h-4 w-4 mr-2" />
            Sign Out
          </Button>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Upload Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                Upload Image
              </CardTitle>
              <CardDescription>
                Upload an image of arecanut leaves or fruit to detect diseases
              </CardDescription>
            </CardHeader>
            <CardContent>
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleImageSelect}
                className="hidden"
              />
              
              <div className="space-y-4">
                <Button 
                  onClick={triggerFileInput} 
                  variant="outline" 
                  className="w-full h-32 border-dashed"
                >
                  <div className="text-center">
                    <FileImage className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                    <span>Click to select image</span>
                  </div>
                </Button>
                
                {imagePreview && (
                  <div className="relative">
                    <img 
                      src={imagePreview} 
                      alt="Selected" 
                      className="w-full h-64 object-cover rounded-lg border"
                    />
                  </div>
                )}
                
                <Button 
                  onClick={handleAnalyzeImage}
                  disabled={!selectedImage || analyzing}
                  className="w-full"
                >
                  {analyzing ? (
                    <>
                      <Camera className="h-4 w-4 mr-2 animate-pulse" />
                      Analyzing...
                    </>
                  ) : (
                    <>
                      <Camera className="h-4 w-4 mr-2" />
                      Analyze Image
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Results Section */}
          <Card>
            <CardHeader>
              <CardTitle>Analysis Results</CardTitle>
              <CardDescription>
                AI-powered disease detection results will appear here
              </CardDescription>
            </CardHeader>
            <CardContent>
              {!analysisResult && !analyzing && (
                <div className="text-center py-8 text-muted-foreground">
                  No analysis performed yet. Upload and analyze an image to see results.
                </div>
              )}
              
              {analyzing && (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-agricultural-600 mx-auto mb-4"></div>
                  <p className="text-muted-foreground">Analyzing image with AI model...</p>
                </div>
              )}
              
              {analysisResult && (
                <div className="space-y-4">
                  <div className="p-4 rounded-lg bg-agricultural-50 border border-agricultural-200">
                    <h3 className="font-semibold text-agricultural-900 mb-2">Detection Result:</h3>
                    <p className="text-agricultural-800">{analysisResult}</p>
                  </div>
                  
                  <div className="text-sm text-muted-foreground">
                    <p>Analysis completed using trained machine learning model</p>
                    <p>For best results, ensure good lighting and clear image quality</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;