classdef LoginPopup < handle
    % LoginPopup - Modal login dialog with glassmorphism effect
    % Provides secure user authentication in a popup window
    
    properties
        Parent          % Parent container
        PopupPanel      % Main popup panel
        OverlayPanel    % Background overlay
        LoginContainer  % Login form container
        UsernameEdit    % Username input field
        PasswordEdit    % Password input field
        LoginButton     % Login submit button
        SignupLink      % Link to signup
        CloseButton     % Close popup button
        Config          % App configuration
        Callbacks       % Callback functions
        IsVisible       % Visibility state
    end
    
    methods
        function obj = LoginPopup(parent, config, callbacks)
            % Constructor - Create login popup
            obj.Parent = parent;
            obj.Config = config;
            obj.Callbacks = callbacks;
            obj.IsVisible = false;
            obj.createLoginPopup();
        end
        
        function createLoginPopup(obj)
            % Create the login popup structure
            theme = obj.Config.getCurrentTheme();
            glass = obj.Config.getGlassEffect();
            
            % Background overlay (semi-transparent)
            obj.OverlayPanel = uipanel(obj.Parent, ...
                'Position', [0 0 1200 800], ...
                'BackgroundColor', [0 0 0], ...
                'BorderType', 'none', ...
                'Visible', 'off');
            
            % Set overlay transparency (simulated with lighter color)
            obj.OverlayPanel.BackgroundColor = [0.2 0.2 0.2];
            
            % Main popup panel (centered)
            obj.PopupPanel = uipanel(obj.OverlayPanel, ...
                'Position', [350 200 500 400], ...
                'BackgroundColor', glass.background, ...
                'BorderType', 'line', ...
                'BorderWidth', 2, ...
                'HighlightColor', glass.border);
            
            % Close button (X)
            obj.CloseButton = uibutton(obj.PopupPanel, 'push', ...
                'Text', '✕', ...
                'FontSize', 16, 'FontWeight', 'bold', ...
                'FontColor', theme.text_secondary, ...
                'BackgroundColor', glass.background, ...
                'Position', [460 360 30 30], ...
                'ButtonPushedFcn', @(~,~) obj.hide());
            
            % Login form container
            obj.LoginContainer = uipanel(obj.PopupPanel, ...
                'Position', [50 50 400 300], ...
                'BackgroundColor', theme.surface, ...
                'BorderType', 'line', ...
                'BorderWidth', 1, ...
                'HighlightColor', glass.border);
            
            obj.createLoginForm();
        end
        
        function createLoginForm(obj)
            % Create the login form elements
            theme = obj.Config.getCurrentTheme();
            
            % Welcome header
            uilabel(obj.LoginContainer, ...
                'Text', '🔐 Welcome Back', ...
                'FontSize', 24, 'FontWeight', 'bold', ...
                'FontColor', theme.text_primary, ...
                'HorizontalAlignment', 'center', ...
                'Position', [50 250 300 40]);
            
            uilabel(obj.LoginContainer, ...
                'Text', 'Sign in to access your medical dashboard', ...
                'FontSize', 14, 'FontColor', theme.text_secondary, ...
                'HorizontalAlignment', 'center', ...
                'Position', [50 220 300 25]);
            
            % Username field
            uilabel(obj.LoginContainer, ...
                'Text', 'USERNAME', ...
                'FontSize', 12, 'FontWeight', 'bold', ...
                'FontColor', theme.text_primary, ...
                'Position', [50 180 150 20]);
            
            obj.UsernameEdit = uieditfield(obj.LoginContainer, 'text', ...
                'Placeholder', 'Enter your username', ...
                'Position', [50 150 300 35], ...
                'FontSize', 14, ...
                'BackgroundColor', theme.surface, ...
                'FontColor', theme.text_primary);
            
            % Password field
            uilabel(obj.LoginContainer, ...
                'Text', 'PASSWORD', ...
                'FontSize', 12, 'FontWeight', 'bold', ...
                'FontColor', theme.text_primary, ...
                'Position', [50 110 150 20]);
            
            obj.PasswordEdit = uieditfield(obj.LoginContainer, 'text', ...
                'Placeholder', 'Enter your password', ...
                'Position', [50 80 250 35], ...
                'FontSize', 14, ...
                'BackgroundColor', theme.surface, ...
                'FontColor', theme.text_primary, ...
                'ValueChangedFcn', @(~,~) obj.maskPassword());
            
            % Show/Hide password button
            uibutton(obj.LoginContainer, 'push', ...
                'Text', '👁️', ...
                'FontSize', 14, ...
                'Position', [310 80 35 35], ...
                'BackgroundColor', theme.primary, ...
                'FontColor', [1 1 1], ...
                'Tooltip', 'Show/Hide password', ...
                'ButtonPushedFcn', @(~,~) obj.togglePasswordVisibility());
            
            % Login button
            obj.LoginButton = uibutton(obj.LoginContainer, 'push', ...
                'Text', 'SIGN IN', ...
                'FontSize', 16, 'FontWeight', 'bold', ...
                'FontColor', [1 1 1], ...
                'BackgroundColor', theme.primary, ...
                'Position', [50 30 300 40], ...
                'ButtonPushedFcn', @(~,~) obj.handleLogin());
            
            % Signup link
            obj.SignupLink = uibutton(obj.LoginContainer, 'push', ...
                'Text', 'Don''t have an account? Sign Up', ...
                'FontSize', 12, ...
                'FontColor', theme.accent, ...
                'BackgroundColor', theme.surface, ...
                'Position', [50 -10 300 25], ...
                'ButtonPushedFcn', @(~,~) obj.switchToSignup());
        end
        
        function maskPassword(obj)
            % Mask password input (placeholder - MATLAB limitation)
            % In a real implementation, this would mask the characters
            % For now, we'll just indicate it's a password field
        end
        
        function togglePasswordVisibility(obj)
            % Toggle password visibility (placeholder)
            % MATLAB doesn't have built-in password masking toggle
            % This is a placeholder for the functionality
        end
        
        function handleLogin(obj)
            % Handle login form submission
            username = obj.UsernameEdit.Value;
            password = obj.PasswordEdit.Value;
            
            % Validate inputs
            if isempty(username) || isempty(password)
                obj.showError('Please enter both username and password');
                return;
            end
            
            % Call authentication callback
            if isfield(obj.Callbacks, 'authenticate')
                success = obj.Callbacks.authenticate(username, password);
                if success
                    obj.hide();
                    if isfield(obj.Callbacks, 'onLoginSuccess')
                        obj.Callbacks.onLoginSuccess(username);
                    end
                else
                    obj.showError('Invalid username or password');
                end
            end
        end
        
        function switchToSignup(obj)
            % Switch to signup popup
            obj.hide();
            if isfield(obj.Callbacks, 'showSignupPopup')
                obj.Callbacks.showSignupPopup();
            end
        end
        
        function showError(obj, message)
            % Show error message
            % Create temporary error label
            theme = obj.Config.getCurrentTheme();
            
            errorLabel = uilabel(obj.LoginContainer, ...
                'Text', ['⚠️ ' message], ...
                'FontSize', 11, 'FontColor', [0.8 0.2 0.2], ...
                'HorizontalAlignment', 'center', ...
                'Position', [50 5 300 20]);
            
            % Auto-hide error after 3 seconds
            timer('StartDelay', 3, 'TimerFcn', @(~,~) delete(errorLabel), ...
                  'ExecutionMode', 'singleShot');
        end
        
        function show(obj)
            % Show the login popup
            obj.OverlayPanel.Visible = 'on';
            obj.IsVisible = true;
            
            % Clear previous inputs
            obj.UsernameEdit.Value = '';
            obj.PasswordEdit.Value = '';
            
            % Focus on username field
            focus(obj.UsernameEdit);
            
            % Add animation effect (placeholder)
            obj.animateShow();
        end
        
        function hide(obj)
            % Hide the login popup
            obj.OverlayPanel.Visible = 'off';
            obj.IsVisible = false;
            
            % Add animation effect (placeholder)
            obj.animateHide();
        end
        
        function animateShow(obj)
            % Animate popup appearance (placeholder)
            % In a full implementation, this would include smooth transitions
            % For now, just immediate show
        end
        
        function animateHide(obj)
            % Animate popup disappearance (placeholder)
            % In a full implementation, this would include smooth transitions
            % For now, just immediate hide
        end
        
        function updateTheme(obj)
            % Update popup theme
            theme = obj.Config.getCurrentTheme();
            glass = obj.Config.getGlassEffect();
            
            % Update popup appearance
            obj.PopupPanel.BackgroundColor = glass.background;
            obj.PopupPanel.HighlightColor = glass.border;
            obj.LoginContainer.BackgroundColor = theme.surface;
            
            % Update form elements
            obj.UsernameEdit.BackgroundColor = theme.surface;
            obj.UsernameEdit.FontColor = theme.text_primary;
            obj.PasswordEdit.BackgroundColor = theme.surface;
            obj.PasswordEdit.FontColor = theme.text_primary;
            obj.LoginButton.BackgroundColor = theme.primary;
            obj.SignupLink.FontColor = theme.accent;
            obj.SignupLink.BackgroundColor = theme.surface;
        end
        
        function visible = isVisible(obj)
            % Check if popup is visible
            visible = obj.IsVisible;
        end
        
        function setCallbacks(obj, callbacks)
            % Update callback functions
            obj.Callbacks = callbacks;
        end
        
        function clearForm(obj)
            % Clear form inputs
            obj.UsernameEdit.Value = '';
            obj.PasswordEdit.Value = '';
        end
    end
end
