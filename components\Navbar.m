classdef Navbar < handle
    % Navbar - Navigation bar component with authentication buttons
    % Provides navigation, theme toggle, and authentication access
    
    properties
        Parent          % Parent container
        NavbarPanel     % Main navbar panel
        LogoLabel       % Application logo/title
        MenuButtons     % Navigation menu buttons
        AuthButtons     % Login/Signup buttons
        ThemeToggle     % Dark/light mode toggle
        UserInfo        % User information display
        WelcomeLabel    % Welcome message label
        LogoutBtn       % Logout button
        Config          % App configuration
        Callbacks       % Callback functions
    end
    
    methods
        function obj = Navbar(parent, config, callbacks)
            % Constructor - Create navbar component
            obj.Parent = parent;
            obj.Config = config;
            obj.Callbacks = callbacks;
            obj.createNavbar();
        end
        
        function createNavbar(obj)
            % Create the main navbar structure
            theme = obj.Config.getCurrentTheme();
            
            % Main navbar panel
            obj.NavbarPanel = uipanel(obj.Parent, ...
                'Position', [0 750 1200 50], ...
                'BackgroundColor', theme.primary, ...
                'BorderType', 'none');
            
            % Logo and title
            obj.LogoLabel = uilabel(obj.NavbarPanel, ...
                'Text', '🌿 ARECANUT AI', ...
                'FontSize', 18, 'FontWeight', 'bold', ...
                'FontColor', [1 1 1], ...
                'Position', [20 10 200 30]);
            
            % Navigation menu buttons
            obj.createMenuButtons();
            
            % Authentication buttons (right side)
            obj.createAuthButtons();
            
            % Theme toggle button
            obj.createThemeToggle();
        end
        
        function createMenuButtons(obj)
            % Create navigation menu buttons
            theme = obj.Config.getCurrentTheme();
            
            menuItems = {'Home', 'About', 'Features', 'Contact'};
            buttonWidth = 80;
            startX = 250;
            
            obj.MenuButtons = {};
            
            for i = 1:length(menuItems)
                xPos = startX + (i-1) * (buttonWidth + 10);
                
                obj.MenuButtons{i} = uibutton(obj.NavbarPanel, 'push', ...
                    'Text', menuItems{i}, ...
                    'FontSize', 12, 'FontWeight', 'normal', ...
                    'FontColor', [1 1 1], ...
                    'BackgroundColor', theme.primary, ...
                    'Position', [xPos 10 buttonWidth 30], ...
                    'ButtonPushedFcn', @(~,~) obj.handleMenuClick(menuItems{i}));
                
                % Add hover effect
                obj.addHoverEffect(obj.MenuButtons{i});
            end
        end
        
        function createAuthButtons(obj)
            % Create login and signup buttons
            theme = obj.Config.getCurrentTheme();
            
            % Login button
            obj.AuthButtons.Login = uibutton(obj.NavbarPanel, 'push', ...
                'Text', 'Login', ...
                'FontSize', 12, 'FontWeight', 'bold', ...
                'FontColor', theme.primary, ...
                'BackgroundColor', [1 1 1], ...
                'Position', [950 10 80 30], ...
                'ButtonPushedFcn', @(~,~) obj.handleLoginClick());
            
            % Signup button
            obj.AuthButtons.Signup = uibutton(obj.NavbarPanel, 'push', ...
                'Text', 'Sign Up', ...
                'FontSize', 12, 'FontWeight', 'bold', ...
                'FontColor', [1 1 1], ...
                'BackgroundColor', theme.accent, ...
                'Position', [1040 10 80 30], ...
                'ButtonPushedFcn', @(~,~) obj.handleSignupClick());
            
            % User info panel (hidden initially)
            obj.UserInfo = uipanel(obj.NavbarPanel, ...
                'Position', [900 10 200 30], ...
                'BackgroundColor', theme.primary, ...
                'BorderType', 'none', ...
                'Visible', 'off');

            % Welcome message (store as separate property)
            obj.WelcomeLabel = uilabel(obj.UserInfo, ...
                'Text', 'Welcome, User!', ...
                'FontSize', 11, 'FontColor', [1 1 1], ...
                'Position', [10 5 120 20]);

            % Logout button (store as separate property)
            obj.LogoutBtn = uibutton(obj.UserInfo, 'push', ...
                'Text', 'Logout', ...
                'FontSize', 10, 'FontColor', [1 1 1], ...
                'BackgroundColor', [0.8 0.3 0.3], ...
                'Position', [140 5 50 20], ...
                'ButtonPushedFcn', @(~,~) obj.handleLogoutClick());
        end
        
        function createThemeToggle(obj)
            % Create theme toggle button
            theme = obj.Config.getCurrentTheme();
            
            if obj.Config.isDarkModeEnabled()
                toggleText = '☀️';
                tooltip = 'Switch to Light Mode';
            else
                toggleText = '🌙';
                tooltip = 'Switch to Dark Mode';
            end
            
            obj.ThemeToggle = uibutton(obj.NavbarPanel, 'push', ...
                'Text', toggleText, ...
                'FontSize', 16, ...
                'FontColor', [1 1 1], ...
                'BackgroundColor', theme.primary, ...
                'Position', [1130 10 30 30], ...
                'Tooltip', tooltip, ...
                'ButtonPushedFcn', @(~,~) obj.handleThemeToggle());
        end
        
        function addHoverEffect(obj, button)
            % Add hover effect to menu buttons
            % Note: MATLAB doesn't have built-in hover events, 
            % but we can simulate with mouse enter/exit if needed
            % This is a placeholder for future enhancement
        end
        
        function handleMenuClick(obj, menuItem)
            % Handle navigation menu clicks
            switch menuItem
                case 'Home'
                    obj.scrollToSection('hero');
                case 'About'
                    obj.scrollToSection('about');
                case 'Features'
                    obj.scrollToSection('features');
                case 'Contact'
                    obj.scrollToSection('contact');
            end
        end
        
        function scrollToSection(obj, sectionId)
            % Scroll to specific section (placeholder for smooth scrolling)
            if isfield(obj.Callbacks, 'scrollToSection')
                obj.Callbacks.scrollToSection(sectionId);
            end
        end
        
        function handleLoginClick(obj)
            % Handle login button click
            if isfield(obj.Callbacks, 'showLoginPopup')
                obj.Callbacks.showLoginPopup();
            end
        end
        
        function handleSignupClick(obj)
            % Handle signup button click
            if isfield(obj.Callbacks, 'showSignupPopup')
                obj.Callbacks.showSignupPopup();
            end
        end
        
        function handleLogoutClick(obj)
            % Handle logout button click
            obj.showAuthButtons();
            if isfield(obj.Callbacks, 'logout')
                obj.Callbacks.logout();
            end
        end
        
        function handleThemeToggle(obj)
            % Handle theme toggle
            obj.Config.toggleTheme();
            obj.updateTheme();
            
            if isfield(obj.Callbacks, 'updateTheme')
                obj.Callbacks.updateTheme();
            end
        end
        
        function updateTheme(obj)
            % Update navbar appearance for new theme
            theme = obj.Config.getCurrentTheme();
            
            % Update navbar background
            obj.NavbarPanel.BackgroundColor = theme.primary;
            
            % Update menu buttons
            for i = 1:length(obj.MenuButtons)
                obj.MenuButtons{i}.BackgroundColor = theme.primary;
            end
            
            % Update auth buttons
            obj.AuthButtons.Login.FontColor = theme.primary;
            obj.AuthButtons.Signup.BackgroundColor = theme.accent;
            
            % Update theme toggle icon
            if obj.Config.isDarkModeEnabled()
                obj.ThemeToggle.Text = '☀️';
                obj.ThemeToggle.Tooltip = 'Switch to Light Mode';
            else
                obj.ThemeToggle.Text = '🌙';
                obj.ThemeToggle.Tooltip = 'Switch to Dark Mode';
            end
        end
        
        function showUserInfo(obj, username)
            % Show user information after login
            obj.WelcomeLabel.Text = sprintf('Welcome, %s!', username);
            obj.UserInfo.Visible = 'on';
            obj.AuthButtons.Login.Visible = 'off';
            obj.AuthButtons.Signup.Visible = 'off';
        end
        
        function showAuthButtons(obj)
            % Show authentication buttons after logout
            obj.UserInfo.Visible = 'off';
            obj.AuthButtons.Login.Visible = 'on';
            obj.AuthButtons.Signup.Visible = 'on';
        end
        
        function setVisible(obj, visible)
            % Set navbar visibility
            obj.NavbarPanel.Visible = visible;
        end
    end
end
