classdef SignupPopup < handle
    % SignupPopup - Modal signup dialog with glassmorphism effect
    % Provides user registration in a popup window
    
    properties
        Parent          % Parent container
        PopupPanel      % Main popup panel
        OverlayPanel    % Background overlay
        SignupContainer % Signup form container
        NameEdit        % Full name input
        PhoneEdit       % Phone number input
        EmailEdit       % Email input
        UsernameEdit    % Username input
        PasswordEdit    % Password input
        PasswordButton  % Set password button
        SignupButton    % Signup submit button
        LoginLink       % Link to login
        CloseButton     % Close popup button
        Config          % App configuration
        Callbacks       % Callback functions
        IsVisible       % Visibility state
        PasswordSet     % Password set status
    end
    
    methods
        function obj = SignupPopup(parent, config, callbacks)
            % Constructor - Create signup popup
            obj.Parent = parent;
            obj.Config = config;
            obj.Callbacks = callbacks;
            obj.IsVisible = false;
            obj.PasswordSet = false;
            obj.createSignupPopup();
        end
        
        function createSignupPopup(obj)
            % Create the signup popup structure
            theme = obj.Config.getCurrentTheme();
            glass = obj.Config.getGlassEffect();
            
            % Background overlay (semi-transparent)
            obj.OverlayPanel = uipanel(obj.Parent, ...
                'Position', [0 0 1200 800], ...
                'BackgroundColor', [0.2 0.2 0.2], ...
                'BorderType', 'none', ...
                'Visible', 'off');
            
            % Main popup panel (centered, larger for signup)
            obj.PopupPanel = uipanel(obj.OverlayPanel, ...
                'Position', [250 100 700 600], ...
                'BackgroundColor', glass.background, ...
                'BorderType', 'line', ...
                'BorderWidth', 2, ...
                'HighlightColor', glass.border);
            
            % Close button (X)
            obj.CloseButton = uibutton(obj.PopupPanel, 'push', ...
                'Text', '✕', ...
                'FontSize', 16, 'FontWeight', 'bold', ...
                'FontColor', theme.text_secondary, ...
                'BackgroundColor', glass.background, ...
                'Position', [660 560 30 30], ...
                'ButtonPushedFcn', @(~,~) obj.hide());
            
            % Signup form container
            obj.SignupContainer = uipanel(obj.PopupPanel, ...
                'Position', [50 50 600 500], ...
                'BackgroundColor', theme.surface, ...
                'BorderType', 'line', ...
                'BorderWidth', 1, ...
                'HighlightColor', glass.border);
            
            obj.createSignupForm();
        end
        
        function createSignupForm(obj)
            % Create the signup form elements
            theme = obj.Config.getCurrentTheme();
            
            % Welcome header
            uilabel(obj.SignupContainer, ...
                'Text', '✨ Create Account', ...
                'FontSize', 24, 'FontWeight', 'bold', ...
                'FontColor', theme.text_primary, ...
                'HorizontalAlignment', 'center', ...
                'Position', [150 450 300 40]);
            
            uilabel(obj.SignupContainer, ...
                'Text', 'Join our medical AI platform today', ...
                'FontSize', 14, 'FontColor', theme.text_secondary, ...
                'HorizontalAlignment', 'center', ...
                'Position', [150 420 300 25]);
            
            % Full name field
            uilabel(obj.SignupContainer, ...
                'Text', 'FULL NAME', ...
                'FontSize', 12, 'FontWeight', 'bold', ...
                'FontColor', theme.text_primary, ...
                'Position', [50 380 150 20]);
            
            obj.NameEdit = uieditfield(obj.SignupContainer, 'text', ...
                'Placeholder', 'Enter your full name', ...
                'Position', [50 350 500 30], ...
                'FontSize', 13, ...
                'BackgroundColor', theme.surface, ...
                'FontColor', theme.text_primary);
            
            % Phone number field
            uilabel(obj.SignupContainer, ...
                'Text', 'PHONE NUMBER', ...
                'FontSize', 12, 'FontWeight', 'bold', ...
                'FontColor', theme.text_primary, ...
                'Position', [50 320 150 20]);
            
            obj.PhoneEdit = uieditfield(obj.SignupContainer, 'text', ...
                'Placeholder', 'Enter your phone number', ...
                'Position', [50 290 500 30], ...
                'FontSize', 13, ...
                'BackgroundColor', theme.surface, ...
                'FontColor', theme.text_primary);
            
            % Email field
            uilabel(obj.SignupContainer, ...
                'Text', 'EMAIL ADDRESS', ...
                'FontSize', 12, 'FontWeight', 'bold', ...
                'FontColor', theme.text_primary, ...
                'Position', [50 260 150 20]);
            
            obj.EmailEdit = uieditfield(obj.SignupContainer, 'text', ...
                'Placeholder', 'Enter your email address', ...
                'Position', [50 230 500 30], ...
                'FontSize', 13, ...
                'BackgroundColor', theme.surface, ...
                'FontColor', theme.text_primary);
            
            % Username field
            uilabel(obj.SignupContainer, ...
                'Text', 'USERNAME (lowercase only)', ...
                'FontSize', 12, 'FontWeight', 'bold', ...
                'FontColor', theme.text_primary, ...
                'Position', [50 200 250 20]);
            
            obj.UsernameEdit = uieditfield(obj.SignupContainer, 'text', ...
                'Placeholder', 'Choose your username', ...
                'Position', [50 170 500 30], ...
                'FontSize', 13, ...
                'BackgroundColor', theme.surface, ...
                'FontColor', theme.text_primary);
            
            % Password field
            uilabel(obj.SignupContainer, ...
                'Text', 'PASSWORD (8 chars: 1 upper, 1 lower, 1 number)', ...
                'FontSize', 11, 'FontWeight', 'bold', ...
                'FontColor', theme.text_primary, ...
                'Position', [50 140 400 20]);
            
            obj.PasswordEdit = uieditfield(obj.SignupContainer, 'text', ...
                'Placeholder', 'Password will be set using secure dialog', ...
                'Position', [50 110 350 30], ...
                'FontSize', 12, ...
                'BackgroundColor', theme.surface, ...
                'FontColor', theme.text_primary, ...
                'Editable', 'off');
            
            % Set password button
            obj.PasswordButton = uibutton(obj.SignupContainer, 'push', ...
                'Text', 'SET PASSWORD', ...
                'FontSize', 11, 'FontWeight', 'bold', ...
                'FontColor', [1 1 1], ...
                'BackgroundColor', theme.secondary, ...
                'Position', [410 110 140 30], ...
                'ButtonPushedFcn', @(~,~) obj.setPassword());
            
            % Terms and conditions
            uilabel(obj.SignupContainer, ...
                'Text', 'By creating an account, you agree to our Terms & Privacy Policy', ...
                'FontSize', 10, 'FontColor', theme.text_secondary, ...
                'HorizontalAlignment', 'center', ...
                'Position', [150 80 300 15]);
            
            % Signup button
            obj.SignupButton = uibutton(obj.SignupContainer, 'push', ...
                'Text', 'CREATE ACCOUNT', ...
                'FontSize', 15, 'FontWeight', 'bold', ...
                'FontColor', [1 1 1], ...
                'BackgroundColor', theme.secondary, ...
                'Position', [50 40 500 35], ...
                'ButtonPushedFcn', @(~,~) obj.handleSignup());
            
            % Login link
            obj.LoginLink = uibutton(obj.SignupContainer, 'push', ...
                'Text', 'Already have an account? Sign In', ...
                'FontSize', 11, ...
                'FontColor', theme.accent, ...
                'BackgroundColor', theme.surface, ...
                'Position', [50 5 500 25], ...
                'ButtonPushedFcn', @(~,~) obj.switchToLogin());
        end
        
        function setPassword(obj)
            % Handle password setting with secure dialog
            password = inputdlg('Enter your password (8+ chars, 1 upper, 1 lower, 1 number):', ...
                'Set Password', 1, {''}, 'on');
            
            if ~isempty(password) && ~isempty(password{1})
                if obj.validatePassword(password{1})
                    obj.PasswordEdit.Value = 'Password set successfully ✓';
                    obj.PasswordEdit.FontColor = [0.2 0.7 0.3];
                    obj.PasswordSet = true;
                    obj.PasswordButton.Text = 'CHANGE PASSWORD';
                else
                    obj.showError('Password must be 8+ characters with 1 uppercase, 1 lowercase, and 1 number');
                end
            end
        end
        
        function valid = validatePassword(obj, password)
            % Validate password requirements
            valid = length(password) >= 8 && ...
                    any(isstrprop(password, 'upper')) && ...
                    any(isstrprop(password, 'lower')) && ...
                    any(isstrprop(password, 'digit'));
        end
        
        function handleSignup(obj)
            % Handle signup form submission
            name = obj.NameEdit.Value;
            phone = obj.PhoneEdit.Value;
            email = obj.EmailEdit.Value;
            username = obj.UsernameEdit.Value;
            
            % Validate inputs
            if isempty(name) || isempty(phone) || isempty(email) || isempty(username)
                obj.showError('Please fill in all fields');
                return;
            end
            
            if ~obj.PasswordSet
                obj.showError('Please set a password');
                return;
            end
            
            if ~obj.validateEmail(email)
                obj.showError('Please enter a valid email address');
                return;
            end
            
            % Call registration callback
            if isfield(obj.Callbacks, 'register')
                userData = struct('name', name, 'phone', phone, 'email', email, 'username', username);
                success = obj.Callbacks.register(userData);
                if success
                    obj.hide();
                    if isfield(obj.Callbacks, 'onSignupSuccess')
                        obj.Callbacks.onSignupSuccess(username);
                    end
                else
                    obj.showError('Registration failed. Username may already exist.');
                end
            end
        end
        
        function valid = validateEmail(obj, email)
            % Basic email validation
            valid = contains(email, '@') && contains(email, '.');
        end
        
        function switchToLogin(obj)
            % Switch to login popup
            obj.hide();
            if isfield(obj.Callbacks, 'showLoginPopup')
                obj.Callbacks.showLoginPopup();
            end
        end
        
        function showError(obj, message)
            % Show error message
            theme = obj.Config.getCurrentTheme();
            
            errorLabel = uilabel(obj.SignupContainer, ...
                'Text', ['⚠️ ' message], ...
                'FontSize', 11, 'FontColor', [0.8 0.2 0.2], ...
                'HorizontalAlignment', 'center', ...
                'Position', [50 -15 500 20]);
            
            % Auto-hide error after 3 seconds
            timer('StartDelay', 3, 'TimerFcn', @(~,~) delete(errorLabel), ...
                  'ExecutionMode', 'singleShot');
        end
        
        function show(obj)
            % Show the signup popup
            obj.OverlayPanel.Visible = 'on';
            obj.IsVisible = true;
            
            % Clear previous inputs
            obj.clearForm();
            
            % Focus on name field
            focus(obj.NameEdit);
        end
        
        function hide(obj)
            % Hide the signup popup
            obj.OverlayPanel.Visible = 'off';
            obj.IsVisible = false;
        end
        
        function clearForm(obj)
            % Clear form inputs
            obj.NameEdit.Value = '';
            obj.PhoneEdit.Value = '';
            obj.EmailEdit.Value = '';
            obj.UsernameEdit.Value = '';
            obj.PasswordEdit.Value = '';
            obj.PasswordEdit.FontColor = obj.Config.getCurrentTheme().text_primary;
            obj.PasswordSet = false;
            obj.PasswordButton.Text = 'SET PASSWORD';
        end
        
        function updateTheme(obj)
            % Update popup theme
            theme = obj.Config.getCurrentTheme();
            glass = obj.Config.getGlassEffect();
            
            % Update popup appearance
            obj.PopupPanel.BackgroundColor = glass.background;
            obj.PopupPanel.HighlightColor = glass.border;
            obj.SignupContainer.BackgroundColor = theme.surface;
            
            % Update form elements
            obj.NameEdit.BackgroundColor = theme.surface;
            obj.PhoneEdit.BackgroundColor = theme.surface;
            obj.EmailEdit.BackgroundColor = theme.surface;
            obj.UsernameEdit.BackgroundColor = theme.surface;
            obj.PasswordEdit.BackgroundColor = theme.surface;
            obj.PasswordButton.BackgroundColor = theme.secondary;
            obj.SignupButton.BackgroundColor = theme.secondary;
            obj.LoginLink.FontColor = theme.accent;
        end
        
        function visible = isVisible(obj)
            % Check if popup is visible
            visible = obj.IsVisible;
        end
    end
end
