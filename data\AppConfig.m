classdef AppConfig < handle
    % AppConfig - Application configuration and settings
    % This class manages all application-wide settings, themes, and constants
    
    properties (Constant)
        % Application Info
        APP_NAME = 'Arecanut Disease Detection Platform'
        APP_VERSION = '2.0.0'
        APP_DESCRIPTION = 'AI-Powered Medical Diagnosis for Arecanut Diseases'
        
        % Window Settings
        WINDOW_WIDTH = 1200
        WINDOW_HEIGHT = 800
        WINDOW_POSITION = [100 100 1200 800]
        
        % Color Themes
        LIGHT_THEME = struct(...
            'background', [0.98 0.99 1], ...
            'surface', [1 1 1], ...
            'primary', [0.15 0.45 0.25], ...
            'secondary', [0.4 0.2 0.6], ...
            'accent', [0.2 0.4 0.8], ...
            'text_primary', [0.1 0.1 0.1], ...
            'text_secondary', [0.4 0.4 0.4], ...
            'border', [0.9 0.9 0.9] ...
        )
        
        DARK_THEME = struct(...
            'background', [0.1 0.1 0.12], ...
            'surface', [0.15 0.15 0.18], ...
            'primary', [0.2 0.6 0.3], ...
            'secondary', [0.6 0.3 0.8], ...
            'accent', [0.3 0.5 0.9], ...
            'text_primary', [0.9 0.9 0.9], ...
            'text_secondary', [0.7 0.7 0.7], ...
            'border', [0.3 0.3 0.3] ...
        )
        
        % Glassmorphism Effects
        GLASS_LIGHT = struct(...
            'background', [0.95 0.97 0.99], ...
            'border', [0.9 0.94 0.98], ...
            'opacity', 0.8 ...
        )
        
        GLASS_DARK = struct(...
            'background', [0.2 0.2 0.25], ...
            'border', [0.3 0.3 0.35], ...
            'opacity', 0.9 ...
        )
        
        % Animation Settings
        TRANSITION_DURATION = 0.3
        FADE_DURATION = 0.2
        SLIDE_DURATION = 0.4
        
        % Database Settings
        DB_NAME = 'arecanut_medical_ai'
        USERS_COLLECTION = 'users'
        DETECTIONS_COLLECTION = 'detections'
        
        % File Paths
        MODEL_PATH = 'resnet50_arecanut_model.mat'
        USER_DATA_PATH = 'user_data.mat'
        
        % Disease Categories
        DISEASE_TYPES = {'High_Severity', 'Medium_Severity', 'Low_Severity'}
        SEVERITY_COLORS = {[0.8 0.2 0.2], [0.9 0.6 0.2], [0.2 0.7 0.3]}
    end
    
    properties (Access = private)
        currentTheme
        isDarkMode
    end
    
    methods
        function obj = AppConfig()
            % Constructor - Initialize with light theme
            obj.isDarkMode = false;
            obj.currentTheme = obj.LIGHT_THEME;
        end
        
        function theme = getCurrentTheme(obj)
            % Get current theme settings
            theme = obj.currentTheme;
        end
        
        function toggleTheme(obj)
            % Toggle between light and dark themes
            obj.isDarkMode = ~obj.isDarkMode;
            if obj.isDarkMode
                obj.currentTheme = obj.DARK_THEME;
            else
                obj.currentTheme = obj.LIGHT_THEME;
            end
        end
        
        function glass = getGlassEffect(obj)
            % Get glassmorphism effect for current theme
            if obj.isDarkMode
                glass = obj.GLASS_DARK;
            else
                glass = obj.GLASS_LIGHT;
            end
        end
        
        function isDark = isDarkModeEnabled(obj)
            % Check if dark mode is enabled
            isDark = obj.isDarkMode;
        end
        
        function color = getSeverityColor(obj, severity)
            % Get color for disease severity level
            switch lower(severity)
                case 'high_severity'
                    color = obj.SEVERITY_COLORS{1};
                case 'medium_severity'
                    color = obj.SEVERITY_COLORS{2};
                case 'low_severity'
                    color = obj.SEVERITY_COLORS{3};
                otherwise
                    color = [0.5 0.5 0.5]; % Default gray
            end
        end
    end
end
