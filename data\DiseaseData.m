classdef DiseaseData < handle
    % DiseaseData - Comprehensive arecanut disease information database
    % Contains detailed information about diseases, symptoms, and characteristics
    
    properties (Constant)
        % Disease Information Database
        DISEASES = struct(...
            'High_Severity', struct(...
                'name', 'High Severity Arecanut Disease', ...
                'description', 'Severe fungal infection causing significant crop damage', ...
                'symptoms', {{'Dark brown/black spots on leaves', 'Leaf wilting and yellowing', 'Premature fruit drop', 'Stem cankers', 'Root rot symptoms'}}, ...
                'causes', {{'Excessive moisture', 'Poor drainage', 'High humidity', 'Fungal pathogens', 'Nutrient deficiency'}}, ...
                'severity_level', 'Critical', ...
                'urgency', 'Immediate action required', ...
                'icon', '🔴', ...
                'color', [0.8 0.2 0.2] ...
            ), ...
            'Medium_Severity', struct(...
                'name', 'Medium Severity Arecanut Disease', ...
                'description', 'Moderate infection requiring prompt treatment', ...
                'symptoms', {{'Yellow spots on leaves', 'Mild leaf curling', 'Reduced fruit size', 'Slight discoloration', 'Growth retardation'}}, ...
                'causes', {{'Seasonal weather changes', 'Moderate moisture stress', 'Minor nutrient imbalance', 'Pest activity', 'Inadequate care'}}, ...
                'severity_level', 'Moderate', ...
                'urgency', 'Treatment within 1-2 weeks', ...
                'icon', '🟡', ...
                'color', [0.9 0.6 0.2] ...
            ), ...
            'Low_Severity', struct(...
                'name', 'Low Severity Arecanut Disease', ...
                'description', 'Minor issues with preventive care needed', ...
                'symptoms', {{'Light yellow patches', 'Minor leaf spots', 'Slight growth reduction', 'Early stage symptoms', 'Preventable conditions'}}, ...
                'causes', {{'Environmental stress', 'Minor nutrient deficiency', 'Seasonal variations', 'Early pest presence', 'Maintenance issues'}}, ...
                'severity_level', 'Mild', ...
                'urgency', 'Monitor and prevent', ...
                'icon', '🟢', ...
                'color', [0.2 0.7 0.3] ...
            ) ...
        )
        
        % How It Works Steps
        HOW_IT_WORKS = struct(...
            'step1', struct(...
                'title', 'Upload Image', ...
                'description', 'Take a clear photo of the arecanut leaf showing symptoms', ...
                'icon', '📸', ...
                'details', 'Ensure good lighting and focus on affected areas' ...
            ), ...
            'step2', struct(...
                'title', 'AI Analysis', ...
                'description', 'Our advanced AI analyzes the image for disease patterns', ...
                'icon', '🤖', ...
                'details', 'Deep learning model trained on thousands of samples' ...
            ), ...
            'step3', struct(...
                'title', 'Get Results', ...
                'description', 'Receive detailed diagnosis and treatment recommendations', ...
                'icon', '📋', ...
                'details', 'Comprehensive report with confidence levels and next steps' ...
            ) ...
        )
        
        % Feature Highlights
        FEATURES = struct(...
            'ai_detection', struct(...
                'title', 'AI-Powered Detection', ...
                'description', 'Advanced deep learning for accurate disease identification', ...
                'icon', '🧠', ...
                'benefits', {{'95%+ accuracy', 'Instant results', 'Continuous learning'}} ...
            ), ...
            'expert_recommendations', struct(...
                'title', 'Expert Treatment Plans', ...
                'description', 'Professional treatment recommendations from agricultural experts', ...
                'icon', '👨‍⚕️', ...
                'benefits', {{'Evidence-based treatments', 'Step-by-step guidance', 'Follow-up care'}} ...
            ), ...
            'disease_tracking', struct(...
                'title', 'Disease History Tracking', ...
                'description', 'Monitor disease progression and treatment effectiveness', ...
                'icon', '📊', ...
                'benefits', {{'Progress monitoring', 'Treatment history', 'Trend analysis'}} ...
            ), ...
            'mobile_friendly', struct(...
                'title', 'Mobile-Friendly Interface', ...
                'description', 'Easy-to-use interface optimized for smartphones and tablets', ...
                'icon', '📱', ...
                'benefits', {{'Responsive design', 'Touch-friendly', 'Offline capability'}} ...
            ) ...
        )
    end
    
    methods (Static)
        function diseaseInfo = getDiseaseInfo(diseaseType)
            % Get detailed information for a specific disease type
            if isfield(DiseaseData.DISEASES, diseaseType)
                diseaseInfo = DiseaseData.DISEASES.(diseaseType);
            else
                diseaseInfo = struct();
            end
        end
        
        function allDiseases = getAllDiseases()
            % Get information for all disease types
            allDiseases = DiseaseData.DISEASES;
        end
        
        function steps = getHowItWorksSteps()
            % Get how-it-works process steps
            steps = DiseaseData.HOW_IT_WORKS;
        end
        
        function features = getFeatures()
            % Get application features
            features = DiseaseData.FEATURES;
        end
        
        function symptoms = getSymptomsList(diseaseType)
            % Get symptoms list for a specific disease
            if isfield(DiseaseData.DISEASES, diseaseType)
                symptoms = DiseaseData.DISEASES.(diseaseType).symptoms;
            else
                symptoms = {};
            end
        end
        
        function causes = getCausesList(diseaseType)
            % Get causes list for a specific disease
            if isfield(DiseaseData.DISEASES, diseaseType)
                causes = DiseaseData.DISEASES.(diseaseType).causes;
            else
                causes = {};
            end
        end
    end
end
