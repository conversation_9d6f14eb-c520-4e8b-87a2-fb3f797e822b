classdef TreatmentData < handle
    % TreatmentData - AI-powered treatment recommendations for arecanut diseases
    % Contains comprehensive treatment plans, medications, and care instructions
    
    properties (Constant)
        % Treatment Plans Database
        TREATMENTS = struct(...
            'High_Severity', struct(...
                'immediate_actions', {{'Remove affected leaves immediately', 'Isolate infected plants', 'Apply emergency fungicide treatment', 'Improve drainage around plants', 'Increase air circulation'}}, ...
                'medications', struct(...
                    'primary', struct('name', 'Copper-based fungicide', 'dosage', '2-3ml per liter', 'frequency', 'Every 3 days for 2 weeks'), ...
                    'secondary', struct('name', 'Systemic fungicide (Propiconazole)', 'dosage', '1ml per liter', 'frequency', 'Weekly for 4 weeks'), ...
                    'organic', struct('name', 'Neem oil solution', 'dosage', '5ml per liter', 'frequency', 'Twice weekly') ...
                ), ...
                'care_instructions', {{'Water early morning only', 'Avoid overhead watering', 'Prune affected areas', 'Apply balanced fertilizer', 'Monitor daily for changes'}}, ...
                'prevention', {{'Improve soil drainage', 'Maintain proper spacing', 'Regular pruning', 'Preventive spraying', 'Soil health management'}}, ...
                'timeline', '2-4 weeks for recovery', ...
                'success_rate', '85%', ...
                'cost_estimate', '$15-25 per plant', ...
                'urgency_level', 'Critical - Start immediately' ...
            ), ...
            'Medium_Severity', struct(...
                'immediate_actions', {{'Apply targeted fungicide', 'Remove affected leaves', 'Adjust watering schedule', 'Check soil conditions', 'Monitor plant health'}}, ...
                'medications', struct(...
                    'primary', struct('name', 'Mancozeb fungicide', 'dosage', '2g per liter', 'frequency', 'Weekly for 3 weeks'), ...
                    'secondary', struct('name', 'Bordeaux mixture', 'dosage', '10g per liter', 'frequency', 'Bi-weekly'), ...
                    'organic', struct('name', 'Baking soda solution', 'dosage', '5g per liter', 'frequency', 'Weekly') ...
                ), ...
                'care_instructions', {{'Reduce watering frequency', 'Apply mulch around base', 'Ensure good air circulation', 'Regular inspection', 'Balanced nutrition'}}, ...
                'prevention', {{'Regular monitoring', 'Proper fertilization', 'Seasonal care', 'Pest control', 'Environmental management'}}, ...
                'timeline', '1-3 weeks for improvement', ...
                'success_rate', '92%', ...
                'cost_estimate', '$8-15 per plant', ...
                'urgency_level', 'Moderate - Start within 1 week' ...
            ), ...
            'Low_Severity', struct(...
                'immediate_actions', {{'Monitor closely', 'Adjust care routine', 'Apply preventive treatment', 'Check environmental factors', 'Document symptoms'}}, ...
                'medications', struct(...
                    'primary', struct('name', 'Preventive fungicide spray', 'dosage', '1ml per liter', 'frequency', 'Monthly'), ...
                    'secondary', struct('name', 'Liquid fertilizer', 'dosage', '5ml per liter', 'frequency', 'Bi-weekly'), ...
                    'organic', struct('name', 'Compost tea', 'dosage', 'Diluted 1:10', 'frequency', 'Weekly') ...
                ), ...
                'care_instructions', {{'Maintain regular watering', 'Ensure adequate nutrition', 'Monitor growth', 'Seasonal adjustments', 'Preventive care'}}, ...
                'prevention', {{'Regular health checks', 'Optimal growing conditions', 'Preventive treatments', 'Environmental control', 'Nutrition management'}}, ...
                'timeline', '1-2 weeks for prevention', ...
                'success_rate', '98%', ...
                'cost_estimate', '$3-8 per plant', ...
                'urgency_level', 'Low - Preventive care' ...
            ) ...
        )
        
        % General Care Guidelines
        GENERAL_CARE = struct(...
            'watering', struct(...
                'frequency', 'Every 2-3 days', ...
                'timing', 'Early morning (6-8 AM)', ...
                'method', 'Base watering, avoid leaves', ...
                'amount', '2-3 liters per mature plant' ...
            ), ...
            'fertilization', struct(...
                'schedule', 'Monthly during growing season', ...
                'type', 'Balanced NPK (10:10:10)', ...
                'organic_options', {'Compost', 'Vermicompost', 'Cow manure'}, ...
                'micronutrients', {'Zinc', 'Boron', 'Magnesium'} ...
            ), ...
            'pruning', struct(...
                'frequency', 'Quarterly', ...
                'focus', 'Dead, diseased, damaged branches', ...
                'timing', 'After harvest season', ...
                'tools', 'Clean, sharp pruning shears' ...
            ), ...
            'monitoring', struct(...
                'daily', 'Visual inspection', ...
                'weekly', 'Detailed health check', ...
                'monthly', 'Growth measurements', ...
                'seasonal', 'Comprehensive assessment' ...
            ) ...
        )
        
        % Emergency Protocols
        EMERGENCY_PROTOCOLS = struct(...
            'severe_outbreak', {{'Quarantine affected area', 'Contact agricultural extension', 'Document spread pattern', 'Apply emergency treatment', 'Monitor neighboring plants'}}, ...
            'rapid_spread', {{'Immediate isolation', 'Intensive treatment', 'Daily monitoring', 'Expert consultation', 'Community alert'}}, ...
            'treatment_failure', {{'Reassess diagnosis', 'Change treatment approach', 'Soil testing', 'Expert evaluation', 'Alternative methods'}} ...
        )
    end
    
    methods (Static)
        function treatment = getTreatmentPlan(diseaseType)
            % Get comprehensive treatment plan for specific disease
            if isfield(TreatmentData.TREATMENTS, diseaseType)
                treatment = TreatmentData.TREATMENTS.(diseaseType);
            else
                treatment = struct();
            end
        end
        
        function care = getGeneralCare()
            % Get general care guidelines
            care = TreatmentData.GENERAL_CARE;
        end
        
        function protocols = getEmergencyProtocols()
            % Get emergency protocols
            protocols = TreatmentData.EMERGENCY_PROTOCOLS;
        end
        
        function recommendations = getAIRecommendations(diseaseType, confidence, severity)
            % Generate AI-powered personalized recommendations
            baseTreatment = TreatmentData.getTreatmentPlan(diseaseType);
            
            recommendations = struct();
            recommendations.confidence = confidence;
            recommendations.severity = severity;
            recommendations.treatment_plan = baseTreatment;
            
            % Adjust recommendations based on confidence level
            if confidence > 0.9
                recommendations.reliability = 'High confidence - Follow treatment plan';
            elseif confidence > 0.7
                recommendations.reliability = 'Good confidence - Monitor progress closely';
            else
                recommendations.reliability = 'Lower confidence - Consider expert consultation';
            end
            
            % Add personalized notes
            recommendations.notes = TreatmentData.generatePersonalizedNotes(diseaseType, confidence);
        end
        
        function notes = generatePersonalizedNotes(diseaseType, confidence)
            % Generate personalized treatment notes
            notes = {};
            
            if confidence > 0.9
                notes{end+1} = 'High confidence detection - proceed with recommended treatment';
            else
                notes{end+1} = 'Consider getting a second opinion from local agricultural expert';
            end
            
            switch diseaseType
                case 'High_Severity'
                    notes{end+1} = 'Urgent action required - disease can spread rapidly';
                    notes{end+1} = 'Document progress daily and adjust treatment if needed';
                case 'Medium_Severity'
                    notes{end+1} = 'Good prognosis with proper treatment';
                    notes{end+1} = 'Monitor for improvement within 1-2 weeks';
                case 'Low_Severity'
                    notes{end+1} = 'Excellent recovery prospects with preventive care';
                    notes{end+1} = 'Focus on prevention to avoid progression';
            end
            
            notes{end+1} = 'Keep detailed records of treatments and plant response';
            notes{end+1} = 'Contact support if symptoms worsen or persist';
        end
    end
end
