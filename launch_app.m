function launch_app()
    % launch_app - Simple launcher for the Arecanut Disease Detection Platform
    
    clc;
    fprintf('\n🌿 ARECANUT DISEASE DETECTION PLATFORM v2.0\n');
    fprintf('============================================\n\n');
    
    try
        % Add paths
        addpath(genpath('.'));
        
        % Create the application
        fprintf('🚀 Launching application...\n');
        app = ArecaApp_New();
        
        fprintf('✅ Application launched successfully!\n');
        fprintf('🎯 Features: Landing Page, Popup Auth, Disease Gallery\n\n');
        
    catch ME
        fprintf('❌ Error: %s\n', ME.message);
        if ~isempty(ME.stack)
            fprintf('At: %s (line %d)\n', ME.stack(1).name, ME.stack(1).line);
        end
        
        % Try to provide helpful error information
        if contains(ME.message, 'WelcomeLabel')
            fprintf('\n💡 This appears to be the property assignment issue.\n');
            fprintf('The fix should resolve this. Please check the Navbar.m file.\n');
        end
    end
end
