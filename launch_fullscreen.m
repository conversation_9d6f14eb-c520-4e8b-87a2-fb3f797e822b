function launch_fullscreen()
    % launch_fullscreen - Launch Arecanut Disease Detection in Full Screen
    
    fprintf('\n🖥️ LAUNCHING ARECANUT DISEASE DETECTION - FULL SCREEN MODE\n');
    fprintf('=========================================================\n\n');
    
    try
        % Add paths
        addpath(genpath('.'));
        
        fprintf('📁 Setting up application paths...\n');
        fprintf('🔍 Checking system requirements...\n');
        
        % Check MATLAB version
        matlabVersion = version('-release');
        fprintf('   📋 MATLAB Version: %s\n', matlabVersion);
        
        % Check toolboxes
        if license('test', 'image_toolbox')
            fprintf('   ✅ Image Processing Toolbox: Available\n');
        else
            fprintf('   ⚠️ Image Processing Toolbox: Not available (some features may be limited)\n');
        end
        
        if license('test', 'neural_network_toolbox')
            fprintf('   ✅ Deep Learning Toolbox: Available\n');
        else
            fprintf('   ⚠️ Deep Learning Toolbox: Not available (some features may be limited)\n');
        end
        
        % Check required folders
        folders = {'components', 'pages', 'utils', 'data', 'assets'};
        for i = 1:length(folders)
            if exist(folders{i}, 'dir')
                fprintf('   ✅ Folder %s: Found\n', folders{i});
            else
                fprintf('   ❌ Folder %s: Missing\n', folders{i});
            end
        end
        
        % Check key files
        files = {'data/AppConfig.m', 'data/DiseaseData.m', 'data/TreatmentData.m', ...
                'components/Navbar.m', 'components/LoginPopup.m', 'components/SignupPopup.m', ...
                'pages/LandingPage.m'};
        
        for i = 1:length(files)
            if exist(files{i}, 'file')
                fprintf('   ✅ Component %s: Found\n', files{i});
            else
                fprintf('   ❌ Component %s: Missing\n', files{i});
            end
        end
        
        fprintf('\n🗄️ Initializing database connection...\n');
        fprintf('🤖 Loading AI model...\n');
        fprintf('🖥️ Launching in FULL SCREEN mode...\n\n');
        
        fprintf('⌨️ KEYBOARD SHORTCUTS:\n');
        fprintf('   🔴 ESC - Close application\n');
        fprintf('   🔄 F11 - Toggle fullscreen/windowed mode\n');
        fprintf('   ❌ Ctrl+Q - Quit application\n\n');
        
        fprintf('👤 DEFAULT LOGIN CREDENTIALS:\n');
        fprintf('   Username: testuser | Password: test123\n');
        fprintf('   Username: admin    | Password: admin123\n\n');
        
        fprintf('🚀 Starting application...\n\n');
        
        % Launch the application
        app = ArecaApp_New();
        
        fprintf('\n✅ APPLICATION LAUNCHED SUCCESSFULLY!\n');
        fprintf('🖥️ The application is now running in FULL SCREEN mode.\n');
        fprintf('💡 Use the keyboard shortcuts above to control the window.\n\n');
        
    catch ME
        fprintf('❌ ERROR: Failed to launch application\n');
        fprintf('Error details: %s\n', ME.message);
        if ~isempty(ME.stack)
            fprintf('Stack trace:\n');
            for i = 1:length(ME.stack)
                fprintf('  %s (line %d)\n', ME.stack(i).name, ME.stack(i).line);
            end
        end
        
        fprintf('\n💡 TROUBLESHOOTING:\n');
        fprintf('   1. Ensure MATLAB Image Processing Toolbox is installed\n');
        fprintf('   2. Check that all component files are in the correct folders\n');
        fprintf('   3. Verify the AI model file exists: resnet50_arecanut_model.mat\n');
        fprintf('   4. Try running: addpath(genpath(pwd))\n');
        fprintf('   5. Use keyboard shortcuts to close if window controls are not accessible\n\n');
    end
end
