#!/usr/bin/env python3
"""
MATLAB Model API Server for Arecanut Disease Detection
Provides REST API endpoints for TypeScript frontend
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import matlab.engine
import numpy as np
from PIL import Image
import io
import base64
import os
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)  # Enable CORS for TypeScript frontend

class ArecaModelAPI:
    def __init__(self):
        self.engine = None
        self.model = None
        self.model_loaded = False
        
    def start_matlab_engine(self):
        """Start MATLAB engine and load the model"""
        try:
            logger.info("Starting MATLAB engine...")
            self.engine = matlab.engine.start_matlab()
            
            # Change to the directory containing the .mat file
            model_path = os.path.abspath('.')
            self.engine.cd(model_path)
            
            # Load the trained model
            logger.info("Loading arecanut model...")
            model_data = self.engine.load('resnet50_arecanut_model.mat')
            self.model = model_data['trainedNet']
            self.model_loaded = True
            
            logger.info("✅ MATLAB engine and model loaded successfully!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to start MATLAB engine: {str(e)}")
            return False
    
    def predict_disease(self, image_data):
        """Predict disease from image data"""
        if not self.model_loaded:
            return {"error": "Model not loaded"}
        
        try:
            # Convert base64 image to MATLAB format
            image_bytes = base64.b64decode(image_data.split(',')[1])
            image = Image.open(io.BytesIO(image_bytes))
            
            # Resize to 224x224 (model input size)
            image = image.resize((224, 224))
            image_array = np.array(image)
            
            # Convert to MATLAB array
            matlab_image = matlab.uint8(image_array.tolist())
            
            # Make prediction using MATLAB
            prediction = self.engine.classify(self.model, matlab_image)
            
            # Convert MATLAB result to Python
            disease_type = str(prediction)
            
            # Map prediction to severity and recommendations
            severity_map = {
                'High_Severity': {
                    'severity': 'High',
                    'confidence': 0.95,
                    'recommendation': 'Immediate treatment required! Consult agricultural expert.',
                    'color': '#dc3545',
                    'icon': '🔴'
                },
                'Medium_Severity': {
                    'severity': 'Medium', 
                    'confidence': 0.85,
                    'recommendation': 'Treatment recommended soon. Monitor closely.',
                    'color': '#ffc107',
                    'icon': '🟡'
                },
                'Low_Severity': {
                    'severity': 'Low',
                    'confidence': 0.75,
                    'recommendation': 'Monitor and consider preventive measures.',
                    'color': '#28a745',
                    'icon': '🟢'
                }
            }
            
            result = severity_map.get(disease_type, {
                'severity': 'Unknown',
                'confidence': 0.5,
                'recommendation': 'Please consult an expert for advice.',
                'color': '#6c757d',
                'icon': '🔍'
            })
            
            result['disease_type'] = disease_type
            result['status'] = 'success'
            
            logger.info(f"✅ Prediction successful: {disease_type}")
            return result
            
        except Exception as e:
            logger.error(f"❌ Prediction failed: {str(e)}")
            return {"error": f"Prediction failed: {str(e)}", "status": "error"}

# Initialize the model API
model_api = ArecaModelAPI()

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "matlab_engine": model_api.engine is not None,
        "model_loaded": model_api.model_loaded
    })

@app.route('/initialize', methods=['POST'])
def initialize_model():
    """Initialize MATLAB engine and load model"""
    success = model_api.start_matlab_engine()
    if success:
        return jsonify({"status": "success", "message": "Model initialized successfully"})
    else:
        return jsonify({"status": "error", "message": "Failed to initialize model"}), 500

@app.route('/predict', methods=['POST'])
def predict():
    """Predict disease from uploaded image"""
    try:
        data = request.get_json()
        
        if 'image' not in data:
            return jsonify({"error": "No image data provided", "status": "error"}), 400
        
        # Make prediction
        result = model_api.predict_disease(data['image'])
        
        if 'error' in result:
            return jsonify(result), 500
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"❌ API error: {str(e)}")
        return jsonify({"error": str(e), "status": "error"}), 500

@app.route('/model-info', methods=['GET'])
def model_info():
    """Get model information"""
    return jsonify({
        "model_name": "ResNet-50 Arecanut Disease Detection",
        "input_size": [224, 224, 3],
        "classes": ["High_Severity", "Medium_Severity", "Low_Severity"],
        "framework": "MATLAB Deep Learning Toolbox",
        "status": "ready" if model_api.model_loaded else "not_loaded"
    })

if __name__ == '__main__':
    print("🌿 Starting Arecanut Disease Detection API Server...")
    print("📋 Endpoints:")
    print("   GET  /health      - Health check")
    print("   POST /initialize  - Initialize MATLAB engine")
    print("   POST /predict     - Predict disease from image")
    print("   GET  /model-info  - Get model information")
    print("\n🚀 Starting server on http://localhost:5000")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
