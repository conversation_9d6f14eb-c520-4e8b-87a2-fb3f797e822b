#!/usr/bin/env python3
"""
Mock API Server for Arecanut Disease Detection (No MATLAB Required)
For testing frontend without MATLAB Engine
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import random
import time
import base64
import io
from PIL import Image

app = Flask(__name__)
CORS(app)

@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({
        "status": "healthy",
        "matlab_engine": False,
        "model_loaded": True,
        "service": "Mock Arecanut Disease Detection API"
    })

@app.route('/initialize', methods=['POST'])
def initialize_model():
    return jsonify({
        "status": "success", 
        "message": "Mock model initialized successfully",
        "model": "Mock ResNet-50 Arecanut Disease Detection"
    })

@app.route('/predict', methods=['POST'])
def predict_disease():
    try:
        data = request.get_json()
        
        if not data or 'image' not in data:
            return jsonify({
                "error": "No image data provided", 
                "status": "error"
            }), 400
        
        # Simulate processing time
        time.sleep(2)
        
        # Mock predictions
        mock_results = [
            {
                'disease': 'High Severity Disease',
                'confidence': 92,
                'severity': 'High',
                'description': 'Severe disease detected requiring immediate attention',
                'recommendations': [
                    'Consult agricultural expert immediately',
                    'Apply appropriate fungicide treatment',
                    'Isolate affected plants',
                    'Monitor surrounding plants closely'
                ],
                'color': '#dc3545',
                'icon': '🔴',
                'raw_prediction': 'High_Severity'
            },
            {
                'disease': 'Medium Severity Disease',
                'confidence': 85,
                'severity': 'Medium',
                'description': 'Moderate disease detected requiring treatment',
                'recommendations': [
                    'Apply preventive treatment',
                    'Monitor plant health regularly',
                    'Improve drainage and ventilation',
                    'Consider organic treatment options'
                ],
                'color': '#ffc107',
                'icon': '🟡',
                'raw_prediction': 'Medium_Severity'
            },
            {
                'disease': 'Low Severity Disease',
                'confidence': 78,
                'severity': 'Low',
                'description': 'Minor disease symptoms detected',
                'recommendations': [
                    'Monitor plant condition',
                    'Ensure proper nutrition',
                    'Maintain good hygiene practices',
                    'Consider preventive measures'
                ],
                'color': '#28a745',
                'icon': '🟢',
                'raw_prediction': 'Low_Severity'
            },
            {
                'disease': 'Healthy Plant',
                'confidence': 95,
                'severity': 'None',
                'description': 'No disease detected - plant appears healthy',
                'recommendations': [
                    'Continue current care routine',
                    'Regular monitoring recommended',
                    'Maintain good agricultural practices',
                    'Preventive care as needed'
                ],
                'color': '#28a745',
                'icon': '✅',
                'raw_prediction': 'Healthy'
            }
        ]
        
        # Random result
        result = random.choice(mock_results)
        result.update({
            'status': 'success',
            'model': 'Mock ResNet-50 Arecanut Disease Detection',
            'timestamp': str(time.time())
        })
        
        print(f"🎯 Mock prediction: {result['disease']}")
        return jsonify(result)
        
    except Exception as e:
        return jsonify({
            "error": str(e), 
            "status": "error"
        }), 500

@app.route('/model-info', methods=['GET'])
def model_info():
    return jsonify({
        "model_name": "Mock ResNet-50 Arecanut Disease Detection",
        "input_size": [224, 224, 3],
        "classes": ["High_Severity", "Medium_Severity", "Low_Severity", "Healthy"],
        "framework": "Mock MATLAB Deep Learning Toolbox",
        "version": "1.0-mock",
        "status": "ready"
    })

if __name__ == '__main__':
    print("🌿 Mock Arecanut Disease Detection API Server")
    print("=" * 45)
    print("⚠️  This is a MOCK server for testing frontend")
    print("📋 Available Endpoints:")
    print("   GET  /health      - Health check")
    print("   POST /initialize  - Initialize mock model")
    print("   POST /predict     - Mock disease prediction")
    print("   GET  /model-info  - Get mock model info")
    print("\n🚀 Starting server on http://localhost:5000")
    print("🔗 CORS enabled for TypeScript frontend")
    print("\n💡 To use real MATLAB predictions:")
    print("   1. Install: pip install matlabengine")
    print("   2. Run: python prediction_api.py")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
