classdef LandingPage < handle
    % LandingPage - Main landing page with hero section and content
    % Displays disease information, features, and how-it-works sections
    
    properties
        Parent          % Parent container
        MainPanel       % Main landing page panel
        HeroSection     % Hero section panel
        AboutSection    % About section panel
        FeaturesSection % Features section panel
        HowItWorksSection % How it works section
        ContactSection  % Contact section panel
        Config          % App configuration
        DiseaseData     % Disease information
        ScrollPosition  % Current scroll position
        Callbacks       % Callback functions
    end
    
    methods
        function obj = LandingPage(parent, config, callbacks)
            % Constructor - Create landing page
            obj.Parent = parent;
            obj.Config = config;
            obj.Callbacks = callbacks;
            obj.DiseaseData = DiseaseData();
            obj.ScrollPosition = 0;
            obj.createLandingPage();
        end
        
        function createLandingPage(obj)
            % Create the main landing page structure
            theme = obj.Config.getCurrentTheme();
            
            % Main scrollable panel
            obj.MainPanel = uipanel(obj.Parent, ...
                'Position', [0 0 1200 750], ...
                'BackgroundColor', theme.background, ...
                'BorderType', 'none');
            
            % Create all sections
            obj.createHeroSection();
            obj.createAboutSection();
            obj.createHowItWorksSection();
            obj.createFeaturesSection();
            obj.createContactSection();
        end
        
        function createHeroSection(obj)
            % Create hero section with main call-to-action
            theme = obj.Config.getCurrentTheme();
            
            obj.HeroSection = uipanel(obj.MainPanel, ...
                'Position', [0 450 1200 300], ...
                'BackgroundColor', theme.background, ...
                'BorderType', 'none');
            
            % Hero background with gradient effect
            heroBackground = uipanel(obj.HeroSection, ...
                'Position', [0 0 1200 300], ...
                'BackgroundColor', [0.95 0.98 1], ...
                'BorderType', 'none');
            
            % Main title
            uilabel(heroBackground, ...
                'Text', '🌿 AI-Powered Arecanut Disease Detection', ...
                'FontSize', 32, 'FontWeight', 'bold', ...
                'FontColor', theme.primary, ...
                'HorizontalAlignment', 'center', ...
                'Position', [200 220 800 50]);
            
            % Subtitle
            uilabel(heroBackground, ...
                'Text', 'Protect your crops with advanced artificial intelligence', ...
                'FontSize', 18, 'FontColor', theme.text_secondary, ...
                'HorizontalAlignment', 'center', ...
                'Position', [200 180 800 30]);
            
            % Description
            uilabel(heroBackground, ...
                'Text', 'Upload a photo of your arecanut leaves and get instant disease diagnosis with expert treatment recommendations', ...
                'FontSize', 14, 'FontColor', theme.text_secondary, ...
                'HorizontalAlignment', 'center', ...
                'Position', [200 140 800 30]);
            
            % Call-to-action buttons
            obj.createCTAButtons(heroBackground, theme);
            
            % Feature highlights
            obj.createFeatureHighlights(heroBackground, theme);
        end
        
        function createCTAButtons(obj, parent, theme)
            % Create call-to-action buttons
            
            % Get Started button
            getStartedBtn = uibutton(parent, 'push', ...
                'Text', '🚀 Get Started', ...
                'FontSize', 16, 'FontWeight', 'bold', ...
                'FontColor', [1 1 1], ...
                'BackgroundColor', theme.primary, ...
                'Position', [450 80 150 40], ...
                'ButtonPushedFcn', @(~,~) obj.handleGetStarted());
            
            % Learn More button
            learnMoreBtn = uibutton(parent, 'push', ...
                'Text', '📖 Learn More', ...
                'FontSize', 16, 'FontWeight', 'normal', ...
                'FontColor', theme.primary, ...
                'BackgroundColor', theme.surface, ...
                'Position', [620 80 150 40], ...
                'ButtonPushedFcn', @(~,~) obj.scrollToSection('about'));
        end
        
        function createFeatureHighlights(obj, parent, theme)
            % Create quick feature highlights in hero section
            features = {'⚡ Instant Results', '🎯 95% Accuracy', '👨‍⚕️ Expert Recommendations'};
            
            for i = 1:length(features)
                xPos = 300 + (i-1) * 200;
                uilabel(parent, ...
                    'Text', features{i}, ...
                    'FontSize', 12, 'FontWeight', 'bold', ...
                    'FontColor', theme.accent, ...
                    'HorizontalAlignment', 'center', ...
                    'Position', [xPos 40 180 20]);
            end
        end
        
        function createAboutSection(obj)
            % Create about section with disease information
            theme = obj.Config.getCurrentTheme();
            
            obj.AboutSection = uipanel(obj.MainPanel, ...
                'Position', [0 200 1200 250], ...
                'BackgroundColor', theme.surface, ...
                'BorderType', 'none');
            
            % Section title
            uilabel(obj.AboutSection, ...
                'Text', 'About Arecanut Diseases', ...
                'FontSize', 24, 'FontWeight', 'bold', ...
                'FontColor', theme.text_primary, ...
                'HorizontalAlignment', 'center', ...
                'Position', [400 200 400 40]);
            
            % Disease cards
            obj.createDiseaseCards();
        end
        
        function createDiseaseCards(obj)
            % Create disease information cards
            theme = obj.Config.getCurrentTheme();
            diseases = obj.DiseaseData.getAllDiseases();
            diseaseTypes = fieldnames(diseases);
            
            cardWidth = 350;
            cardHeight = 140;
            startX = 75;
            
            for i = 1:length(diseaseTypes)
                diseaseType = diseaseTypes{i};
                diseaseInfo = diseases.(diseaseType);
                
                xPos = startX + (i-1) * (cardWidth + 25);
                
                % Disease card panel
                cardPanel = uipanel(obj.AboutSection, ...
                    'Position', [xPos 30 cardWidth cardHeight], ...
                    'BackgroundColor', obj.Config.getGlassEffect().background, ...
                    'BorderType', 'line', ...
                    'BorderWidth', 1, ...
                    'HighlightColor', obj.Config.getGlassEffect().border);
                
                % Disease icon and name
                uilabel(cardPanel, ...
                    'Text', [diseaseInfo.icon ' ' diseaseInfo.name], ...
                    'FontSize', 14, 'FontWeight', 'bold', ...
                    'FontColor', diseaseInfo.color, ...
                    'Position', [15 110 320 20]);
                
                % Description
                uilabel(cardPanel, ...
                    'Text', diseaseInfo.description, ...
                    'FontSize', 11, 'FontColor', theme.text_secondary, ...
                    'Position', [15 85 320 20]);
                
                % Severity level
                uilabel(cardPanel, ...
                    'Text', ['Severity: ' diseaseInfo.severity_level], ...
                    'FontSize', 10, 'FontWeight', 'bold', ...
                    'FontColor', diseaseInfo.color, ...
                    'Position', [15 65 150 15]);
                
                % Urgency
                uilabel(cardPanel, ...
                    'Text', diseaseInfo.urgency, ...
                    'FontSize', 10, 'FontColor', theme.text_secondary, ...
                    'Position', [15 45 320 15]);
                
                % Learn more button
                uibutton(cardPanel, 'push', ...
                    'Text', 'Learn More', ...
                    'FontSize', 9, 'FontColor', theme.primary, ...
                    'BackgroundColor', theme.surface, ...
                    'Position', [15 15 80 20], ...
                    'ButtonPushedFcn', @(~,~) obj.showDiseaseDetails(diseaseType));
            end
        end
        
        function createHowItWorksSection(obj)
            % Create how-it-works section
            theme = obj.Config.getCurrentTheme();
            
            obj.HowItWorksSection = uipanel(obj.MainPanel, ...
                'Position', [0 50 1200 150], ...
                'BackgroundColor', theme.background, ...
                'BorderType', 'none');
            
            % Section title
            uilabel(obj.HowItWorksSection, ...
                'Text', 'How It Works', ...
                'FontSize', 24, 'FontWeight', 'bold', ...
                'FontColor', theme.text_primary, ...
                'HorizontalAlignment', 'center', ...
                'Position', [450 110 300 30]);
            
            % Process steps
            obj.createProcessSteps();
        end
        
        function createProcessSteps(obj)
            % Create how-it-works process steps
            theme = obj.Config.getCurrentTheme();
            steps = obj.DiseaseData.getHowItWorksSteps();
            stepFields = fieldnames(steps);
            
            stepWidth = 300;
            startX = 150;
            
            for i = 1:length(stepFields)
                stepInfo = steps.(stepFields{i});
                xPos = startX + (i-1) * (stepWidth + 50);
                
                % Step number circle
                stepCircle = uipanel(obj.HowItWorksSection, ...
                    'Position', [xPos + 125 70 50 50], ...
                    'BackgroundColor', theme.primary, ...
                    'BorderType', 'line', ...
                    'BorderWidth', 2, ...
                    'HighlightColor', theme.primary);
                
                % Step number
                uilabel(stepCircle, ...
                    'Text', num2str(i), ...
                    'FontSize', 20, 'FontWeight', 'bold', ...
                    'FontColor', [1 1 1], ...
                    'HorizontalAlignment', 'center', ...
                    'Position', [15 15 20 20]);
                
                % Step icon and title
                uilabel(obj.HowItWorksSection, ...
                    'Text', [stepInfo.icon ' ' stepInfo.title], ...
                    'FontSize', 14, 'FontWeight', 'bold', ...
                    'FontColor', theme.text_primary, ...
                    'HorizontalAlignment', 'center', ...
                    'Position', [xPos 40 stepWidth 20]);
                
                % Step description
                uilabel(obj.HowItWorksSection, ...
                    'Text', stepInfo.description, ...
                    'FontSize', 11, 'FontColor', theme.text_secondary, ...
                    'HorizontalAlignment', 'center', ...
                    'Position', [xPos 15 stepWidth 20]);
            end
        end
        
        function createFeaturesSection(obj)
            % Create features section (placeholder)
            theme = obj.Config.getCurrentTheme();
            
            obj.FeaturesSection = uipanel(obj.MainPanel, ...
                'Position', [0 -100 1200 150], ...
                'BackgroundColor', theme.surface, ...
                'BorderType', 'none');
            
            % Will be expanded in Phase 2
        end
        
        function createContactSection(obj)
            % Create contact section (placeholder)
            theme = obj.Config.getCurrentTheme();
            
            obj.ContactSection = uipanel(obj.MainPanel, ...
                'Position', [0 -250 1200 150], ...
                'BackgroundColor', theme.background, ...
                'BorderType', 'none');
            
            % Will be expanded in Phase 2
        end
        
        function handleGetStarted(obj)
            % Handle get started button click
            if isfield(obj.Callbacks, 'showLoginPopup')
                obj.Callbacks.showLoginPopup();
            end
        end
        
        function scrollToSection(obj, sectionId)
            % Scroll to specific section
            % This is a placeholder for smooth scrolling implementation
            fprintf('Scrolling to section: %s\n', sectionId);
        end
        
        function showDiseaseDetails(obj, diseaseType)
            % Show detailed disease information
            if isfield(obj.Callbacks, 'showDiseaseDetails')
                obj.Callbacks.showDiseaseDetails(diseaseType);
            end
        end
        
        function updateTheme(obj)
            % Update landing page theme
            theme = obj.Config.getCurrentTheme();
            
            % Update all section backgrounds
            obj.MainPanel.BackgroundColor = theme.background;
            obj.HeroSection.BackgroundColor = theme.background;
            obj.AboutSection.BackgroundColor = theme.surface;
            obj.HowItWorksSection.BackgroundColor = theme.background;
            obj.FeaturesSection.BackgroundColor = theme.surface;
            obj.ContactSection.BackgroundColor = theme.background;
            
            % Recreate sections with new theme
            obj.createLandingPage();
        end
        
        function setVisible(obj, visible)
            % Set landing page visibility
            obj.MainPanel.Visible = visible;
        end
    end
end
