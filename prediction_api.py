#!/usr/bin/env python3
"""
MATLAB Model API for Arecanut Disease Detection
Bridges TypeScript frontend with MATLAB .mat model
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import matlab.engine
import numpy as np
from PIL import Image
import io
import base64
import os
import logging
import json

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)  # Enable CORS for TypeScript frontend

class MATLABModelAPI:
    def __init__(self):
        self.engine = None
        self.model = None
        self.model_loaded = False
        
    def initialize(self):
        """Initialize MATLAB engine and load the model"""
        try:
            logger.info("🔄 Starting MATLAB engine...")
            self.engine = matlab.engine.start_matlab()
            
            # Change to the directory containing the .mat file
            model_dir = os.path.abspath('.')
            self.engine.cd(model_dir)
            logger.info(f"📁 Changed to directory: {model_dir}")
            
            # Load the trained model
            logger.info("📦 Loading arecanut model...")
            model_data = self.engine.load('resnet50_arecanut_model.mat')
            self.model = model_data['trainedNet']
            self.model_loaded = True
            
            logger.info("✅ MATLAB engine and model loaded successfully!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize MATLAB: {str(e)}")
            return False
    
    def predict(self, image_base64):
        """Predict disease from base64 image"""
        if not self.model_loaded:
            return {"error": "Model not loaded", "status": "error"}
        
        try:
            # Decode base64 image
            if ',' in image_base64:
                image_data = image_base64.split(',')[1]
            else:
                image_data = image_base64
                
            image_bytes = base64.b64decode(image_data)
            image = Image.open(io.BytesIO(image_bytes))
            
            # Convert to RGB if needed
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # Resize to model input size (224x224)
            image = image.resize((224, 224))
            image_array = np.array(image)
            
            # Convert to MATLAB format
            matlab_image = matlab.uint8(image_array.tolist())
            
            logger.info("🔍 Making prediction with MATLAB model...")
            
            # Make prediction
            prediction = self.engine.classify(self.model, matlab_image)
            disease_type = str(prediction)
            
            logger.info(f"🎯 Prediction result: {disease_type}")
            
            # Map predictions to user-friendly results
            disease_mapping = {
                'High_Severity': {
                    'disease': 'High Severity Disease',
                    'confidence': 92,
                    'severity': 'High',
                    'description': 'Severe disease detected requiring immediate attention',
                    'recommendations': [
                        'Consult agricultural expert immediately',
                        'Apply appropriate fungicide treatment',
                        'Isolate affected plants',
                        'Monitor surrounding plants closely'
                    ],
                    'color': '#dc3545',
                    'icon': '🔴'
                },
                'Medium_Severity': {
                    'disease': 'Medium Severity Disease',
                    'confidence': 85,
                    'severity': 'Medium',
                    'description': 'Moderate disease detected requiring treatment',
                    'recommendations': [
                        'Apply preventive treatment',
                        'Monitor plant health regularly',
                        'Improve drainage and ventilation',
                        'Consider organic treatment options'
                    ],
                    'color': '#ffc107',
                    'icon': '🟡'
                },
                'Low_Severity': {
                    'disease': 'Low Severity Disease',
                    'confidence': 78,
                    'severity': 'Low',
                    'description': 'Minor disease symptoms detected',
                    'recommendations': [
                        'Monitor plant condition',
                        'Ensure proper nutrition',
                        'Maintain good hygiene practices',
                        'Consider preventive measures'
                    ],
                    'color': '#28a745',
                    'icon': '🟢'
                }
            }
            
            result = disease_mapping.get(disease_type, {
                'disease': 'Unknown Condition',
                'confidence': 50,
                'severity': 'Unknown',
                'description': 'Unable to classify the condition',
                'recommendations': ['Consult an agricultural expert'],
                'color': '#6c757d',
                'icon': '❓'
            })
            
            result.update({
                'status': 'success',
                'raw_prediction': disease_type,
                'model': 'ResNet-50 Arecanut Disease Detection',
                'timestamp': str(np.datetime64('now'))
            })
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Prediction failed: {str(e)}")
            return {
                "error": f"Prediction failed: {str(e)}", 
                "status": "error"
            }

# Initialize the model API
model_api = MATLABModelAPI()

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "matlab_engine": model_api.engine is not None,
        "model_loaded": model_api.model_loaded,
        "service": "Arecanut Disease Detection API"
    })

@app.route('/initialize', methods=['POST'])
def initialize_model():
    """Initialize MATLAB engine and load model"""
    logger.info("🚀 Initializing MATLAB model...")
    success = model_api.initialize()
    
    if success:
        return jsonify({
            "status": "success", 
            "message": "MATLAB model initialized successfully",
            "model": "ResNet-50 Arecanut Disease Detection"
        })
    else:
        return jsonify({
            "status": "error", 
            "message": "Failed to initialize MATLAB model"
        }), 500

@app.route('/predict', methods=['POST'])
def predict_disease():
    """Predict disease from uploaded image"""
    try:
        data = request.get_json()
        
        if not data or 'image' not in data:
            return jsonify({
                "error": "No image data provided", 
                "status": "error"
            }), 400
        
        logger.info("📸 Received image for prediction")
        
        # Make prediction
        result = model_api.predict(data['image'])
        
        if result.get('status') == 'error':
            return jsonify(result), 500
        
        logger.info("✅ Prediction completed successfully")
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"❌ API error: {str(e)}")
        return jsonify({
            "error": str(e), 
            "status": "error"
        }), 500

@app.route('/model-info', methods=['GET'])
def model_info():
    """Get model information"""
    return jsonify({
        "model_name": "ResNet-50 Arecanut Disease Detection",
        "input_size": [224, 224, 3],
        "classes": ["High_Severity", "Medium_Severity", "Low_Severity"],
        "framework": "MATLAB Deep Learning Toolbox",
        "version": "1.0",
        "status": "ready" if model_api.model_loaded else "not_loaded"
    })

if __name__ == '__main__':
    print("🌿 Arecanut Disease Detection API Server")
    print("=" * 40)
    print("📋 Available Endpoints:")
    print("   GET  /health      - Health check")
    print("   POST /initialize  - Initialize MATLAB engine")
    print("   POST /predict     - Predict disease from image")
    print("   GET  /model-info  - Get model information")
    print("\n🚀 Starting server on http://localhost:5000")
    print("🔗 CORS enabled for TypeScript frontend")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
