function run_app()
    % run_app - Simple and reliable launcher for Arecanut Disease Detection Platform
    
    clc;
    fprintf('\n🌿 ARECANUT DISEASE DETECTION PLATFORM v2.0\n');
    fprintf('===========================================\n\n');
    
    try
        % Clear any existing variables
        clear;
        
        % Add all necessary paths
        fprintf('📁 Setting up paths...\n');
        addpath(genpath(pwd));
        
        % Create the application
        fprintf('🚀 Creating application...\n');
        app = ArecaApp_New();
        
        fprintf('\n✅ APPLICATION LAUNCHED SUCCESSFULLY!\n\n');
        
        fprintf('🎯 FEATURES AVAILABLE:\n');
        fprintf('   ✨ Modern Landing Page with Disease Information\n');
        fprintf('   🔐 Popup Authentication System (Login/Signup)\n');
        fprintf('   📊 Disease Gallery with Detailed Symptoms\n');
        fprintf('   💊 AI Treatment Recommendations\n');
        fprintf('   🌙 Dark/Light Mode Toggle\n');
        fprintf('   📱 Responsive Professional Interface\n');
        fprintf('   🗄️ MongoDB Integration with Local Storage Fallback\n');
        fprintf('   📈 Detection History and Analytics\n\n');
        
        fprintf('👤 DEFAULT TEST CREDENTIALS:\n');
        fprintf('   Username: testuser\n');
        fprintf('   Password: test123\n\n');
        
        fprintf('📋 HOW TO USE:\n');
        fprintf('   1. Browse the landing page to learn about diseases\n');
        fprintf('   2. Click "Login" in the navbar to authenticate\n');
        fprintf('   3. Use the test credentials above\n');
        fprintf('   4. Access the AI detection dashboard after login\n');
        fprintf('   5. Upload arecanut leaf images for analysis\n\n');
        
        fprintf('🎨 UI FEATURES:\n');
        fprintf('   • Full screen mode for better usability\n');
        fprintf('   • Keyboard shortcuts (ESC, F11, Ctrl+Q)\n');
        fprintf('   • Glassmorphism popup effects\n');
        fprintf('   • Professional medical color scheme\n');
        fprintf('   • Organized component architecture\n');
        fprintf('   • Smooth navigation and transitions\n\n');
        
        fprintf('Ready to use! 🎉\n\n');
        
        % Return the app instance for further use
        assignin('base', 'arecaApp', app);
        fprintf('💡 App instance saved as "arecaApp" in workspace\n\n');
        
    catch ME
        fprintf('❌ ERROR: %s\n', ME.message);
        
        if ~isempty(ME.stack)
            fprintf('\n📍 Error location: %s (line %d)\n', ME.stack(1).name, ME.stack(1).line);
        end
        
        fprintf('\n🔧 TROUBLESHOOTING:\n');
        fprintf('   1. Ensure you are in the correct directory\n');
        fprintf('   2. Check that all component files exist\n');
        fprintf('   3. Try: addpath(genpath(pwd))\n');
        fprintf('   4. Verify MATLAB version compatibility\n\n');
        
        % Show available files for debugging
        fprintf('📂 Current directory contents:\n');
        files = dir('*.m');
        for i = 1:length(files)
            fprintf('   %s\n', files(i).name);
        end
        
        folders = {'components', 'pages', 'data', 'utils', 'assets'};
        fprintf('\n📁 Required folders:\n');
        for i = 1:length(folders)
            if exist(folders{i}, 'dir')
                fprintf('   ✅ %s\n', folders{i});
            else
                fprintf('   ❌ %s (missing)\n', folders{i});
            end
        end
    end
end
