function app = run_app()
    % RUN_APP - Launch ArecaApp with MongoDB support
    
    fprintf('🌿 Loading MongoDB drivers...\n');
    
    % Add MongoDB JAR files to classpath
    try
        javaaddpath('C:\mongodb-jars\bson-4.11.0.jar');
        javaaddpath('C:\mongodb-jars\mongodb-driver-core-4.11.0.jar');
        javaaddpath('C:\mongodb-jars\mongodb-driver-sync-4.11.0.jar');
        fprintf('✅ MongoDB JAR files loaded\n');
    catch ME
        error('❌ Failed to load MongoDB JARs: %s', ME.message);
    end
    
    % Test MongoDB classes
    try
        javaMethod('valueOf', 'org.bson.BsonType', 'DOCUMENT');
        fprintf('✅ MongoDB classes accessible\n');
    catch ME
        warning('⚠️ MongoDB classes test failed: %s', ME.message);
    end
    
    % Launch ArecaApp
    fprintf('🚀 Starting ArecaApp...\n');
    app = ArecaApp;
    fprintf('✅ ArecaApp launched successfully!\n');
end
