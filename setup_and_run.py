#!/usr/bin/env python3
"""
Setup and Run Script for Arecanut Disease Detection System
Handles all setup and launches both API server and TypeScript frontend
"""

import subprocess
import sys
import os
import time
import threading
import webbrowser
from pathlib import Path

def print_banner():
    print("🌿" + "=" * 60 + "🌿")
    print("    ARECANUT DISEASE DETECTION SYSTEM SETUP")
    print("🌿" + "=" * 60 + "🌿")
    print()

def check_requirements():
    """Check if all requirements are met"""
    print("🔍 Checking system requirements...")
    
    # Check Python
    try:
        python_version = sys.version_info
        if python_version.major >= 3 and python_version.minor >= 8:
            print(f"   ✅ Python {python_version.major}.{python_version.minor} found")
        else:
            print(f"   ❌ Python 3.8+ required, found {python_version.major}.{python_version.minor}")
            return False
    except:
        print("   ❌ Python not found")
        return False
    
    # Check Node.js
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"   ✅ Node.js found: {result.stdout.strip()}")
        else:
            print("   ❌ Node.js not found")
            return False
    except:
        print("   ❌ Node.js not found")
        return False
    
    # Check MATLAB
    try:
        import matlab.engine
        print("   ✅ MATLAB Engine for Python found")
    except ImportError:
        print("   ❌ MATLAB Engine for Python not found")
        print("      Install with: pip install matlabengine")
        return False
    
    # Check required files
    required_files = [
        'resnet50_arecanut_model.mat',
        'areca-nut/package.json',
        'prediction_api.py'
    ]
    
    for file in required_files:
        if os.path.exists(file):
            print(f"   ✅ Found: {file}")
        else:
            print(f"   ❌ Missing: {file}")
            return False
    
    return True

def install_python_dependencies():
    """Install Python dependencies"""
    print("\n📦 Installing Python dependencies...")
    
    dependencies = [
        'flask',
        'flask-cors',
        'pillow',
        'numpy'
    ]
    
    for dep in dependencies:
        try:
            print(f"   Installing {dep}...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', dep], 
                         check=True, capture_output=True)
            print(f"   ✅ {dep} installed")
        except subprocess.CalledProcessError:
            print(f"   ❌ Failed to install {dep}")
            return False
    
    return True

def install_node_dependencies():
    """Install Node.js dependencies"""
    print("\n📦 Installing Node.js dependencies...")
    
    os.chdir('areca-nut')
    
    try:
        print("   Running npm install...")
        subprocess.run(['npm', 'install'], check=True)
        print("   ✅ Node.js dependencies installed")
        return True
    except subprocess.CalledProcessError:
        print("   ❌ Failed to install Node.js dependencies")
        return False
    finally:
        os.chdir('..')

def start_api_server():
    """Start the Python API server"""
    print("\n🚀 Starting Python API server...")
    
    try:
        # Start the API server in a separate process
        process = subprocess.Popen([
            sys.executable, 'prediction_api.py'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait a moment for server to start
        time.sleep(3)
        
        # Check if process is still running
        if process.poll() is None:
            print("   ✅ API server started on http://localhost:5000")
            return process
        else:
            print("   ❌ API server failed to start")
            return None
            
    except Exception as e:
        print(f"   ❌ Failed to start API server: {e}")
        return None

def start_frontend():
    """Start the TypeScript frontend"""
    print("\n🚀 Starting TypeScript frontend...")
    
    os.chdir('areca-nut')
    
    try:
        # Start the frontend development server
        process = subprocess.Popen([
            'npm', 'run', 'dev'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait a moment for server to start
        time.sleep(5)
        
        # Check if process is still running
        if process.poll() is None:
            print("   ✅ Frontend started on http://localhost:8080")
            return process
        else:
            print("   ❌ Frontend failed to start")
            return None
            
    except Exception as e:
        print(f"   ❌ Failed to start frontend: {e}")
        return None
    finally:
        os.chdir('..')

def initialize_matlab_model():
    """Initialize the MATLAB model"""
    print("\n🧠 Initializing MATLAB model...")
    
    try:
        import requests
        
        # Wait for API server to be ready
        for i in range(10):
            try:
                response = requests.get('http://localhost:5000/health', timeout=2)
                if response.status_code == 200:
                    break
            except:
                time.sleep(1)
        
        # Initialize the model
        response = requests.post('http://localhost:5000/initialize', timeout=30)
        
        if response.status_code == 200:
            print("   ✅ MATLAB model initialized successfully")
            return True
        else:
            print("   ❌ Failed to initialize MATLAB model")
            return False
            
    except Exception as e:
        print(f"   ❌ Model initialization error: {e}")
        return False

def main():
    print_banner()
    
    # Check requirements
    if not check_requirements():
        print("\n❌ Requirements check failed. Please fix the issues above.")
        return
    
    # Install dependencies
    if not install_python_dependencies():
        print("\n❌ Failed to install Python dependencies.")
        return
    
    if not install_node_dependencies():
        print("\n❌ Failed to install Node.js dependencies.")
        return
    
    # Start services
    api_process = start_api_server()
    if not api_process:
        print("\n❌ Failed to start API server.")
        return
    
    frontend_process = start_frontend()
    if not frontend_process:
        print("\n❌ Failed to start frontend.")
        api_process.terminate()
        return
    
    # Initialize MATLAB model
    if not initialize_matlab_model():
        print("\n⚠️ MATLAB model initialization failed, but services are running.")
    
    print("\n🎉 SYSTEM READY!")
    print("=" * 50)
    print("🌐 Frontend: http://localhost:8080")
    print("🔗 API: http://localhost:5000")
    print("📱 Open your browser and navigate to the frontend URL")
    print("\n📋 Usage:")
    print("1. Register/Login to your account")
    print("2. Go to Dashboard")
    print("3. Upload an arecanut leaf image")
    print("4. Click 'Analyze Image' for AI prediction")
    print("\n⏹️ Press Ctrl+C to stop all services")
    
    # Open browser
    time.sleep(2)
    webbrowser.open('http://localhost:8080')
    
    try:
        # Keep running until interrupted
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n\n🛑 Shutting down services...")
        api_process.terminate()
        frontend_process.terminate()
        print("✅ All services stopped.")

if __name__ == '__main__':
    main()
