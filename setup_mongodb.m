function setup_mongodb()
    % Setup MongoDB JAR files and test connection
    
    fprintf('🔧 Setting up MongoDB for ArecaApp...\n\n');
    
    % Define JAR file paths
    jarPath = 'C:\mongodb-jars\';
    jarFiles = {
        'bson-4.11.0.jar',
        'mongodb-driver-core-4.11.0.jar',
        'mongodb-driver-sync-4.11.0.jar'
    };
    
    % Step 1: Add JAR files to classpath
    fprintf('Step 1: Adding MongoDB JAR files to MATLAB classpath...\n');
    for i = 1:length(jarFiles)
        jarFile = fullfile(jarPath, jarFiles{i});
        if exist(jarFile, 'file')
            javaaddpath(jarFile);
            fprintf('✅ Added: %s\n', jarFiles{i});
        else
            fprintf('❌ Not found: %s\n', jarFile);
            return;
        end
    end
    
    % Step 2: Test imports
    fprintf('\nStep 2: Testing MongoDB imports...\n');
    try
        import com.mongodb.client.MongoClients
        import org.bson.Document
        fprintf('✅ MongoDB imports successful\n');
    catch ME
        fprintf('❌ Import failed: %s\n', ME.message);
        return;
    end
    
    % Step 3: Test MongoDB connection (optional - only if MongoDB is running)
    fprintf('\nStep 3: Testing MongoDB connection...\n');
    try
        uri = "mongodb://localhost:27017";
        client = MongoClients.create(uri);
        db = client.getDatabase("arecanut_app");
        collection = db.getCollection("users");
        fprintf('✅ MongoDB connection successful\n');
        fprintf('   Database: arecanut_app\n');
        fprintf('   Collection: users\n');
    catch ME
        fprintf('⚠️  MongoDB connection failed: %s\n', ME.message);
        fprintf('   This is OK if MongoDB is not running yet.\n');
        fprintf('   To start MongoDB: net start MongoDB\n');
    end
    
    % Step 4: Test ArecaApp
    fprintf('\nStep 4: Testing ArecaApp...\n');
    try
        app = ArecaApp;
        fprintf('✅ ArecaApp created successfully\n');
        
        % Test MongoDB methods
        if app.isMongoDBAvailable()
            fprintf('✅ MongoDB integration ready\n');
        else
            fprintf('⚠️  MongoDB not available, will use fallback\n');
        end
        
        % Show the app
        app.UIFigure.Visible = 'on';
        fprintf('✅ ArecaApp launched successfully\n');
        
    catch ME
        fprintf('❌ ArecaApp failed: %s\n', ME.message);
        return;
    end
    
    fprintf('\n🎉 MongoDB setup complete!\n');
    fprintf('\nNext steps:\n');
    fprintf('1. Start MongoDB service: net start MongoDB\n');
    fprintf('2. Your ArecaApp is now running with MongoDB support\n');
    fprintf('3. Register a new user to test the database\n');
    fprintf('4. Check data in MongoDB: mongosh -> use arecanut_app -> db.users.find()\n');
end
