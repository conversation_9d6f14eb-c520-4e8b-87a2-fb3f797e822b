function app = start_arecanut_app()
    % START_ARECANUT_APP - Launch the Arecanut Disease Detection App with MongoDB
    % This function automatically loads MongoDB JAR files and starts the app
    
    fprintf('🌿 Starting Arecanut Disease Detection - Medical AI Platform...\n\n');
    
    % Add MongoDB JAR files to MATLAB classpath
    fprintf('📦 Loading MongoDB JAR files...\n');
    try
        % Check if JAR files exist
        jarPath = 'C:\mongodb-jars\';
        jarFiles = {
            'bson-4.11.0.jar'
            'mongodb-driver-core-4.11.0.jar'
            'mongodb-driver-sync-4.11.0.jar'
        };
        
        for i = 1:length(jarFiles)
            jarFile = fullfile(jarPath, jarFiles{i});
            if exist(jarFile, 'file')
                javaaddpath(jarFile);
                fprintf('   ✅ Loaded: %s\n', jarFiles{i});
            else
                error('JAR file not found: %s', jarFile);
            end
        end
        
        fprintf('✅ All MongoDB JAR files loaded successfully\n\n');
    catch ME
        fprintf('❌ Could not load MongoDB JAR files: %s\n', ME.message);
        fprintf('\n📋 SETUP INSTRUCTIONS:\n');
        fprintf('1. Create directory: C:\\mongodb-jars\\\n');
        fprintf('2. Download these JAR files to that directory:\n');
        fprintf('   - bson-4.11.0.jar\n');
        fprintf('   - mongodb-driver-core-4.11.0.jar\n');
        fprintf('   - mongodb-driver-sync-4.11.0.jar\n');
        fprintf('3. Download from: https://mvnrepository.com/artifact/org.mongodb/mongodb-driver-sync/4.11.0\n\n');
        error('MongoDB JAR files are required. Please follow setup instructions above.');
    end
    
    % Verify JAR files are loaded
    fprintf('🔍 Verifying MongoDB classes...\n');
    try
        % Test if we can access MongoDB classes
        javaMethod('valueOf', 'org.bson.BsonType', 'DOCUMENT');
        fprintf('✅ MongoDB classes accessible\n\n');
    catch ME
        warning('⚠️ MongoDB classes verification failed: %s\n', ME.message);
        fprintf('The app will still start, but database features may not work.\n\n');
    end
    
    % Check if MongoDB server is running
    fprintf('🔍 Checking MongoDB server...\n');
    try
        % Try to connect to MongoDB
        import com.mongodb.client.MongoClients
        client = MongoClients.create("mongodb://localhost:27017");
        client.listDatabaseNames();
        client.close();
        fprintf('✅ MongoDB server is running and accessible\n\n');
    catch ME
        fprintf('⚠️ MongoDB server connection failed: %s\n', ME.message);
        fprintf('Please ensure MongoDB server is running on localhost:27017\n\n');
    end
    
    % Launch the app
    fprintf('🚀 Launching ArecaApp...\n');
    try
        app = ArecaApp;
        fprintf('✅ ArecaApp launched successfully!\n\n');
        
        fprintf('🎯 App Features:\n');
        fprintf('   • Enhanced Medical Interface\n');
        fprintf('   • Secure User Authentication\n');
        fprintf('   • AI-Powered Disease Detection\n');
        fprintf('   • MongoDB Database Integration\n');
        fprintf('   • Professional Medical Dashboard\n\n');
        
        fprintf('📋 Usage Instructions:\n');
        fprintf('   1. Register a new account or sign in\n');
        fprintf('   2. Upload arecanut leaf images\n');
        fprintf('   3. Get AI-powered disease analysis\n');
        fprintf('   4. View detailed medical reports\n\n');
        
        fprintf('🎉 Ready to use! MongoDB integration active!\n');
        
    catch ME
        error('❌ Failed to launch ArecaApp: %s', ME.message);
    end
end
