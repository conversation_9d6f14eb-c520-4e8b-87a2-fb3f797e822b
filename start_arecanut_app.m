function app = start_arecanut_app()
    % START_ARECANUT_APP - Launch the Arecanut Disease Detection App
    % This function automatically loads MongoDB JAR files and starts the app
    
    fprintf('🌿 Starting Arecanut Disease Detection - Medical AI Platform...\n\n');
    
    % Add MongoDB JAR files to MATLAB classpath
    fprintf('📦 Loading MongoDB JAR files...\n');
    try
        javaaddpath('C:\mongodb-jars\bson-4.11.0.jar');
        javaaddpath('C:\mongodb-jars\mongodb-driver-core-4.11.0.jar');
        javaaddpath('C:\mongodb-jars\mongodb-driver-sync-4.11.0.jar');
        fprintf('✅ MongoDB JAR files loaded successfully\n\n');
    catch ME
        error('❌ Could not load MongoDB JAR files: %s\nPlease ensure MongoDB JAR files are in C:\\mongodb-jars\\', ME.message);
    end
    
    % Verify JAR files are loaded
    fprintf('🔍 Verifying JAR files...\n');
    try
        % Test if we can access MongoDB classes
        javaMethod('valueOf', 'org.bson.BsonType', 'DOCUMENT');
        fprintf('✅ MongoDB classes accessible\n\n');
    catch ME
        warning('⚠️ MongoDB classes verification failed: %s\n', ME.message);
        fprintf('The app will still start, but database features may not work.\n\n');
    end
    
    % Launch the app
    fprintf('🚀 Launching ArecaApp...\n');
    try
        app = ArecaApp;
        fprintf('✅ ArecaApp launched successfully!\n\n');
        
        fprintf('🎯 App Features:\n');
        fprintf('   • Enhanced Glassmorphism Design\n');
        fprintf('   • Secure User Authentication\n');
        fprintf('   • AI-Powered Disease Detection\n');
        fprintf('   • MongoDB Database Integration\n');
        fprintf('   • Professional Medical Interface\n\n');
        
        fprintf('📋 Usage Instructions:\n');
        fprintf('   1. Register a new account or sign in\n');
        fprintf('   2. Upload arecanut leaf images\n');
        fprintf('   3. Get AI-powered disease analysis\n');
        fprintf('   4. View detailed medical reports\n\n');
        
        fprintf('🎉 Ready to use! Enjoy the enhanced glassmorphism interface!\n');
        
    catch ME
        error('❌ Failed to launch ArecaApp: %s', ME.message);
    end
end
