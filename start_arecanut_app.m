function start_arecanut_app()
    % Start Arecanut Disease Detection App with MongoDB support
    
    fprintf('🌿 Starting Arecanut Disease Detection App...\n');
    
    % Add MongoDB JAR files to classpath
    fprintf('📦 Loading MongoDB drivers...\n');
    javaaddpath('C:\mongodb-jars\bson-4.11.0.jar');
    javaaddpath('C:\mongodb-jars\mongodb-driver-core-4.11.0.jar');
    javaaddpath('C:\mongodb-jars\mongodb-driver-sync-4.11.0.jar');
    
    % Test MongoDB connection
    fprintf('🔗 Testing MongoDB connection...\n');
    try
        import com.mongodb.client.MongoClients
        import org.bson.Document
        
        uri = "mongodb://localhost:27017";
        client = MongoClients.create(uri);
        db = client.getDatabase("arecanut_app");
        collection = db.getCollection("users");
        
        fprintf('✅ MongoDB connected successfully\n');
        fprintf('   Database: arecanut_app\n');
        fprintf('   Collection: users\n');
        
    catch ME
        fprintf('⚠️  MongoDB connection failed: %s\n', ME.message);
        fprintf('   App will still work, but user data won''t persist\n');
        fprintf('   To fix: Start MongoDB service with "net start MongoDB"\n');
    end
    
    % Launch the app
    fprintf('🚀 Launching ArecaApp...\n');
    try
        app = ArecaApp;
        app.UIFigure.Visible = 'on';
        fprintf('✅ ArecaApp launched successfully!\n');
        fprintf('\n🎉 Ready to use!\n');
        fprintf('📝 Features available:\n');
        fprintf('   • User registration and login with MongoDB\n');
        fprintf('   • Arecanut disease detection using AI\n');
        fprintf('   • Secure password hashing\n');
        fprintf('   • Professional medical interface\n');
        
    catch ME
        fprintf('❌ Failed to launch ArecaApp: %s\n', ME.message);
    end
end
