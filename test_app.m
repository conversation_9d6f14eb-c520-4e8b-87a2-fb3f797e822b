function test_app()
    % TEST_APP - Complete test of ArecaApp functionality
    
    fprintf('\n🧪 TESTING ARECANUT APP\n');
    fprintf('======================\n\n');
    
    % Test 1: Check MongoDB JAR files
    fprintf('1️⃣ Checking MongoDB JAR files...\n');
    jarPath = 'C:\mongodb-jars\';
    jarFiles = {
        'bson-4.11.0.jar'
        'mongodb-driver-core-4.11.0.jar'
        'mongodb-driver-sync-4.11.0.jar'
    };
    
    allFound = true;
    for i = 1:length(jarFiles)
        jarFile = fullfile(jarPath, jarFiles{i});
        if exist(jarFile, 'file')
            fprintf('   ✅ Found: %s\n', jarFiles{i});
        else
            fprintf('   ❌ Missing: %s\n', jarFiles{i});
            allFound = false;
        end
    end
    
    if ~allFound
        fprintf('\n❌ Some JAR files are missing!\n');
        fprintf('Please ensure all JAR files are in C:\\mongodb-jars\\\n');
        return;
    end
    
    % Test 2: Check AI model
    fprintf('\n2️⃣ Checking AI model...\n');
    if exist('resnet50_arecanut_model.mat', 'file')
        fprintf('   ✅ AI model found: resnet50_arecanut_model.mat\n');
    else
        fprintf('   ❌ AI model missing: resnet50_arecanut_model.mat\n');
        return;
    end
    
    % Test 3: Test MongoDB connection
    fprintf('\n3️⃣ Testing MongoDB connection...\n');
    try
        % Load JAR files
        javaaddpath('C:\mongodb-jars\bson-4.11.0.jar');
        javaaddpath('C:\mongodb-jars\mongodb-driver-core-4.11.0.jar');
        javaaddpath('C:\mongodb-jars\mongodb-driver-sync-4.11.0.jar');
        
        % Test connection
        client = javaMethod('create', 'com.mongodb.client.MongoClients', 'mongodb://localhost:27017');
        client.listDatabaseNames();
        client.close();
        fprintf('   ✅ MongoDB connection successful\n');
    catch ME
        fprintf('   ❌ MongoDB connection failed: %s\n', ME.message);
        fprintf('   💡 Make sure MongoDB server is running:\n');
        fprintf('      Run in Command Prompt: mongod --dbpath C:\\data\\db\n');
        return;
    end
    
    % Test 4: Launch ArecaApp
    fprintf('\n4️⃣ Launching ArecaApp...\n');
    try
        app = ArecaApp;
        fprintf('   ✅ ArecaApp launched successfully!\n');
        fprintf('   🎯 You can now:\n');
        fprintf('      • Register new users\n');
        fprintf('      • Login with credentials\n');
        fprintf('      • Upload images for AI analysis\n');
        fprintf('      • Get disease detection results\n');
        
        % Keep app open for user interaction
        fprintf('\n🎉 ALL TESTS PASSED!\n');
        fprintf('✅ ArecaApp is ready to use!\n');
        fprintf('📱 The application window should be open now.\n\n');
        
        fprintf('📋 NEXT STEPS:\n');
        fprintf('1. Register a new user account\n');
        fprintf('2. Login with your credentials\n');
        fprintf('3. Upload an arecanut leaf image\n');
        fprintf('4. Click "Analyze Disease" for AI prediction\n\n');
        
        return; % Keep app running
        
    catch ME
        fprintf('   ❌ Failed to launch ArecaApp: %s\n', ME.message);
        return;
    end
    
end
