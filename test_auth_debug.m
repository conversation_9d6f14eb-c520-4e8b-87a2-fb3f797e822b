function test_auth_debug()
    % Test authentication debugging
    
    fprintf('🔍 Testing Authentication Debug...\n\n');
    
    % Add MongoDB JARs
    javaaddpath('C:\mongodb-jars\bson-4.11.0.jar');
    javaaddpath('C:\mongodb-jars\mongodb-driver-core-4.11.0.jar');
    javaaddpath('C:\mongodb-jars\mongodb-driver-sync-4.11.0.jar');
    
    % Create app instance
    app = ArecaApp;
    
    fprintf('✅ App created successfully\n');
    fprintf('📝 Instructions for testing:\n');
    fprintf('1. Register a new user first (use the signup panel)\n');
    fprintf('2. Try to login with the same credentials\n');
    fprintf('3. Check the MATLAB console for debug messages\n');
    fprintf('4. The debug output will show:\n');
    fprintf('   - What username/password you entered\n');
    fprintf('   - Whether the user exists in database\n');
    fprintf('   - Whether password matches\n\n');
    
    fprintf('🔧 Common issues to check:\n');
    fprintf('- Password masking: Make sure actual password is captured correctly\n');
    fprintf('- Username case: Registration converts to lowercase, login should match\n');
    fprintf('- Password hashing: Same algorithm used for registration and login\n');
    fprintf('- MongoDB connection: Ensure database is accessible\n\n');
    
    fprintf('💡 Expected debug output format:\n');
    fprintf('Login button pressed:\n');
    fprintf('  Username: "testuser"\n');
    fprintf('  Password field value: "********"\n');
    fprintf('  Actual password: "TestPass1"\n');
    fprintf('  Password length: 9\n');
    fprintf('Login attempt - Username: "testuser", Password length: 9\n');
    fprintf('✅ Authentication successful\n\n');
    
    fprintf('🚀 App is ready for testing!\n');
end
