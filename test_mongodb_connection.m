function test_mongodb_connection()
    % Test MongoDB connection and basic operations
    
    fprintf('🔍 Testing MongoDB Connection...\n\n');
    
    % Test 1: Check if MongoDB Java driver is available
    fprintf('Test 1: Checking MongoDB Java driver...\n');
    try
        import com.mongodb.client.MongoClients
        fprintf('✅ MongoDB Java driver imported successfully\n');
    catch ME
        fprintf('❌ MongoDB Java driver not found: %s\n', ME.message);
        fprintf('💡 Solution: Download mongodb-driver-sync-4.11.1.jar and add to MATLAB classpath\n');
        fprintf('   javaaddpath(''path/to/mongodb-driver-sync-4.11.1.jar'');\n\n');
        return;
    end
    
    % Test 2: Test MongoDB connection
    fprintf('\nTest 2: Testing MongoDB connection...\n');
    try
        uri = "mongodb://localhost:27017";
        client = MongoClients.create(uri);
        db = client.getDatabase("arecanut_app");
        fprintf('✅ Connected to MongoDB successfully\n');
        fprintf('   Database: arecanut_app\n');
        fprintf('   URI: %s\n', uri);
    catch ME
        fprintf('❌ MongoDB connection failed: %s\n', ME.message);
        fprintf('💡 Solutions:\n');
        fprintf('   - Ensure MongoDB is running: net start MongoDB (Windows)\n');
        fprintf('   - Check if port 27017 is available\n');
        fprintf('   - Verify MongoDB installation\n\n');
        return;
    end
    
    % Test 3: Test collection access
    fprintf('\nTest 3: Testing collection access...\n');
    try
        collection = db.getCollection("users");
        fprintf('✅ Users collection accessible\n');
    catch ME
        fprintf('❌ Collection access failed: %s\n', ME.message);
        return;
    end
    
    % Test 4: Test document operations
    fprintf('\nTest 4: Testing document operations...\n');
    try
        import org.bson.Document
        
        % Create a test document
        testDoc = Document("test", "connection") ...
            .append("timestamp", java.util.Date()) ...
            .append("status", "testing");
        
        % Insert test document
        collection.insertOne(testDoc);
        fprintf('✅ Document insertion successful\n');
        
        % Query test document
        cursor = collection.find(Document("test", "connection"));
        if cursor.iterator().hasNext()
            fprintf('✅ Document query successful\n');
        else
            fprintf('❌ Document query failed\n');
        end
        
        % Clean up test document
        collection.deleteMany(Document("test", "connection"));
        fprintf('✅ Document cleanup successful\n');
        
    catch ME
        fprintf('❌ Document operations failed: %s\n', ME.message);
        return;
    end
    
    % Test 5: Test ArecaApp MongoDB methods
    fprintf('\nTest 5: Testing ArecaApp MongoDB integration...\n');
    try
        app = ArecaApp;
        
        % Test connection method
        db_test = app.connectToMongoDB();
        fprintf('✅ ArecaApp MongoDB connection method works\n');
        
        % Close app
        delete(app.UIFigure);
        
    catch ME
        fprintf('❌ ArecaApp MongoDB integration failed: %s\n', ME.message);
        fprintf('💡 Check ArecaApp.m for MongoDB method implementations\n');
        return;
    end
    
    fprintf('\n🎉 All MongoDB tests passed!\n');
    fprintf('📝 Your system is ready for MongoDB integration\n');
    fprintf('🚀 You can now run ArecaApp with full MongoDB support\n\n');
    
    fprintf('Next steps:\n');
    fprintf('1. Run ArecaApp\n');
    fprintf('2. Register a new user\n');
    fprintf('3. Check MongoDB: use arecanut_app; db.users.find()\n');
end
