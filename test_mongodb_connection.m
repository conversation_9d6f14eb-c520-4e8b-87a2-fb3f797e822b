function test_mongodb_connection()
    % TEST_MONGODB_CONNECTION - Test MongoDB setup and connectivity
    
    fprintf('\n🧪 TESTING MONGODB CONNECTION\n');
    fprintf('============================\n\n');
    
    % Test 1: Check JAR files
    fprintf('1️⃣ Testing JAR files...\n');
    try
        jarPath = 'C:\mongodb-jars\';
        jarFiles = {
            'bson-4.11.0.jar'
            'mongodb-driver-core-4.11.0.jar'
            'mongodb-driver-sync-4.11.0.jar'
        };
        
        allFound = true;
        for i = 1:length(jarFiles)
            jarFile = fullfile(jarPath, jarFiles{i});
            if exist(jarFile, 'file')
                fprintf('   ✅ Found: %s\n', jarFiles{i});
            else
                fprintf('   ❌ Missing: %s\n', jarFiles{i});
                allFound = false;
            end
        end
        
        if allFound
            fprintf('   ✅ All JAR files present\n');
        else
            fprintf('   ❌ Some JAR files missing\n');
            return;
        end
        
    catch ME
        fprintf('   ❌ Error checking JAR files: %s\n', ME.message);
        return;
    end
    
    % Test 2: Load JAR files
    fprintf('\n2️⃣ Loading JAR files...\n');
    try
        for i = 1:length(jarFiles)
            jarFile = fullfile(jarPath, jarFiles{i});
            javaaddpath(jarFile);
        end
        fprintf('   ✅ JAR files loaded successfully\n');
    catch ME
        fprintf('   ❌ Error loading JAR files: %s\n', ME.message);
        return;
    end
    
    % Test 3: Test MongoDB classes
    fprintf('\n3️⃣ Testing MongoDB classes...\n');
    try
        javaMethod('valueOf', 'org.bson.BsonType', 'DOCUMENT');
        fprintf('   ✅ BSON classes accessible\n');
        
        import com.mongodb.client.MongoClients
        fprintf('   ✅ MongoDB client classes accessible\n');
        
        import org.bson.Document
        fprintf('   ✅ Document classes accessible\n');
        
    catch ME
        fprintf('   ❌ Error accessing MongoDB classes: %s\n', ME.message);
        return;
    end
    
    % Test 4: Test MongoDB server connection
    fprintf('\n4️⃣ Testing MongoDB server connection...\n');
    try
        client = MongoClients.create("mongodb://localhost:27017");
        fprintf('   ✅ Connected to MongoDB server\n');
        
        % List databases
        dbNames = client.listDatabaseNames();
        fprintf('   ✅ Can list databases\n');
        
        % Test database operations
        db = client.getDatabase("arecanut_app");
        collection = db.getCollection("test");
        
        % Insert test document
        testDoc = Document("test", "connection").append("timestamp", java.util.Date());
        collection.insertOne(testDoc);
        fprintf('   ✅ Can insert documents\n');
        
        % Find test document
        cursor = collection.find(Document("test", "connection"));
        if cursor.iterator().hasNext()
            fprintf('   ✅ Can query documents\n');
        end
        
        % Clean up test document
        collection.deleteMany(Document("test", "connection"));
        fprintf('   ✅ Can delete documents\n');
        
        client.close();
        fprintf('   ✅ Connection closed successfully\n');
        
    catch ME
        fprintf('   ❌ MongoDB server connection failed: %s\n', ME.message);
        fprintf('   💡 Make sure MongoDB server is running:\n');
        fprintf('      - Windows: Start "MongoDB" service\n');
        fprintf('      - Or run: mongod --dbpath C:\\data\\db\n');
        return;
    end
    
    % Test 5: Test ArecaApp integration
    fprintf('\n5️⃣ Testing ArecaApp integration...\n');
    try
        fprintf('   Creating ArecaApp instance...\n');
        app = ArecaApp;
        fprintf('   ✅ ArecaApp created successfully with MongoDB support\n');
        
        % Close app
        delete(app.UIFigure);
        fprintf('   ✅ ArecaApp closed successfully\n');
        
    catch ME
        fprintf('   ❌ ArecaApp integration failed: %s\n', ME.message);
        return;
    end
    
    fprintf('\n🎉 ALL TESTS PASSED!\n');
    fprintf('✅ MongoDB is properly configured and ready to use\n');
    fprintf('✅ ArecaApp can connect to MongoDB successfully\n\n');
    
    fprintf('🚀 TO START THE APP:\n');
    fprintf('   >> start_arecanut_app\n\n');
    
    fprintf('📋 MONGODB SETUP COMPLETE:\n');
    fprintf('   • JAR files: Loaded\n');
    fprintf('   • MongoDB server: Running\n');
    fprintf('   • Database operations: Working\n');
    fprintf('   • ArecaApp integration: Ready\n\n');
end
