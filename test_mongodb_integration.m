function test_mongodb_integration()
    % test_mongodb_integration - Test MongoDB integration and fallback
    
    fprintf('\n🧪 TESTING MONGODB INTEGRATION\n');
    fprintf('==============================\n\n');
    
    try
        % Add paths
        addpath(genpath('.'));
        
        % Test 1: MongoDB Availability
        fprintf('1️⃣ Testing MongoDB availability...\n');
        mongoAvailable = MongoDBUtils.testMongoDBAvailability();
        if mongoAvailable
            fprintf('   ✅ MongoDB is available and accessible\n');
        else
            fprintf('   ⚠️ MongoDB not available, will use local storage\n');
        end
        
        % Test 2: Database Manager Initialization
        fprintf('\n2️⃣ Testing Database Manager...\n');
        dbManager = DatabaseManager();
        fprintf('   ✅ Database Manager created successfully\n');
        
        % Test 3: User Operations
        fprintf('\n3️⃣ Testing User Operations...\n');
        
        % Test user creation
        testUser = struct();
        testUser.username = 'mongotest';
        testUser.name = 'MongoDB Test User';
        testUser.email = '<EMAIL>';
        testUser.phone = '5555555555';
        
        success = dbManager.insertUser(testUser);
        if success
            fprintf('   ✅ User creation: SUCCESS\n');
        else
            fprintf('   ❌ User creation: FAILED\n');
        end
        
        % Test user authentication
        success = dbManager.authenticateUser('mongotest', 'demo123');
        if success
            fprintf('   ✅ User authentication: SUCCESS\n');
        else
            fprintf('   ❌ User authentication: FAILED\n');
        end
        
        % Test user lookup
        user = dbManager.findUser('mongotest');
        if ~isempty(user)
            fprintf('   ✅ User lookup: SUCCESS\n');
        else
            fprintf('   ❌ User lookup: FAILED\n');
        end
        
        % Test 4: Detection Operations
        fprintf('\n4️⃣ Testing Detection Operations...\n');
        
        detectionData = struct();
        detectionData.username = 'mongotest';
        detectionData.disease_type = 'High_Severity';
        detectionData.confidence = 0.92;
        detectionData.image_path = '/test/image.jpg';
        detectionData.model_version = '1.0';
        
        success = dbManager.insertDetection(detectionData);
        if success
            fprintf('   ✅ Detection insertion: SUCCESS\n');
        else
            fprintf('   ❌ Detection insertion: FAILED\n');
        end
        
        % Test 5: Database Statistics
        fprintf('\n5️⃣ Testing Database Statistics...\n');
        stats = dbManager.getDatabaseStats();
        
        fprintf('   📊 Storage Type: %s\n', stats.primary_storage);
        fprintf('   🗄️ MongoDB Available: %s\n', stats.mongodb_available ? 'Yes' : 'No');
        
        if isfield(stats, 'local_storage')
            fprintf('   👥 Local Users: %d\n', stats.local_storage.users);
            fprintf('   🔬 Local Detections: %d\n', stats.local_storage.detections);
            fprintf('   📝 Local Sessions: %d\n', stats.local_storage.sessions);
        end
        
        if isfield(stats, 'mongodb') && stats.mongodb_available
            fprintf('   🗄️ MongoDB Users: %d\n', stats.mongodb.total_users);
            fprintf('   🗄️ MongoDB Detections: %d\n', stats.mongodb.total_detections);
        end
        
        % Test 6: Application Integration
        fprintf('\n6️⃣ Testing Application Integration...\n');
        
        try
            % Create minimal app instance
            fprintf('   Creating application instance...\n');
            app = ArecaApp_New();
            
            % Test authentication through app
            success = app.authenticateUser('testuser', 'test123');
            if success
                fprintf('   ✅ App authentication: SUCCESS\n');
            else
                fprintf('   ❌ App authentication: FAILED\n');
            end
            
            % Show database stats through app
            app.showDatabaseStats();
            
            fprintf('   ✅ Application integration: SUCCESS\n');
            
            % Clean up
            app.closeApp();
            
        catch ME
            fprintf('   ❌ Application integration error: %s\n', ME.message);
        end
        
        % Clean up
        dbManager.disconnect();
        
        fprintf('\n🎉 MONGODB INTEGRATION TEST COMPLETE!\n\n');
        
        fprintf('📋 SUMMARY:\n');
        fprintf('   🗄️ MongoDB Support: %s\n', mongoAvailable ? 'Available' : 'Not Available');
        fprintf('   💾 Local Storage: Always Available\n');
        fprintf('   🔄 Automatic Fallback: Enabled\n');
        fprintf('   ✅ Database Operations: Working\n');
        fprintf('   🎯 Application Integration: Working\n\n');
        
        fprintf('🚀 READY TO USE:\n');
        fprintf('   The application will automatically use MongoDB if available,\n');
        fprintf('   or fall back to local storage if MongoDB is not accessible.\n\n');
        
        fprintf('💡 TO USE MONGODB:\n');
        fprintf('   1. Install MongoDB on your system\n');
        fprintf('   2. Start MongoDB service\n');
        fprintf('   3. Ensure MATLAB can access MongoDB Java driver\n');
        fprintf('   4. Run the application - it will auto-detect MongoDB\n\n');
        
    catch ME
        fprintf('❌ TEST FAILED: %s\n', ME.message);
        fprintf('Stack trace:\n');
        for i = 1:length(ME.stack)
            fprintf('  %s (line %d)\n', ME.stack(i).name, ME.stack(i).line);
        end
    end
end
