function test_navbar_fix()
    % test_navbar_fix - Test if the Navbar component works after the fix
    
    fprintf('🧪 Testing Navbar Component Fix...\n');
    
    try
        % Add paths
        addpath('data');
        addpath('components');
        
        % Create configuration
        config = AppConfig();
        
        % Create a test figure
        fig = uifigure('Name', 'Navbar Test', 'Position', [100 100 800 200]);
        
        % Create callbacks
        callbacks = struct();
        callbacks.showLoginPopup = @() fprintf('✅ Login popup callback works\n');
        callbacks.showSignupPopup = @() fprintf('✅ Signup popup callback works\n');
        callbacks.scrollToSection = @(section) fprintf('✅ Scroll to %s works\n', section);
        callbacks.updateTheme = @() fprintf('✅ Theme update callback works\n');
        callbacks.logout = @() fprintf('✅ Logout callback works\n');
        
        % Create navbar
        fprintf('Creating Navbar component...\n');
        navbar = Navbar(fig, config, callbacks);
        
        % Test navbar methods
        fprintf('Testing navbar methods...\n');
        
        % Test theme toggle
        navbar.handleThemeToggle();
        
        % Test user info display
        navbar.showUserInfo('TestUser');
        
        % Test auth buttons
        navbar.showAuthButtons();
        
        % Show the figure
        fig.Visible = 'on';
        
        fprintf('✅ SUCCESS: Navbar component works correctly!\n');
        fprintf('🎉 The fix resolved the property assignment issue.\n');
        fprintf('Press any key to close the test window...\n');
        
        pause;
        close(fig);
        
    catch ME
        fprintf('❌ ERROR: %s\n', ME.message);
        fprintf('Stack trace:\n');
        for i = 1:length(ME.stack)
            fprintf('  %s (line %d)\n', ME.stack(i).name, ME.stack(i).line);
        end
    end
end
