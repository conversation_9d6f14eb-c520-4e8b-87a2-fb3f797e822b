function test_new_app()
    % test_new_app - Test the new organized application components
    
    fprintf('\n🧪 Testing New Arecanut App Components...\n\n');
    
    try
        % Add paths
        addpath('data');
        addpath('components');
        addpath('pages');
        addpath('utils');
        
        % Test 1: Configuration
        fprintf('1️⃣ Testing AppConfig...\n');
        config = AppConfig();
        theme = config.getCurrentTheme();
        fprintf('   ✅ Theme loaded: %s mode\n', config.isDarkModeEnabled() ? 'Dark' : 'Light');
        
        % Test 2: Disease Data
        fprintf('2️⃣ Testing DiseaseData...\n');
        diseases = DiseaseData.getAllDiseases();
        diseaseTypes = fieldnames(diseases);
        fprintf('   ✅ Loaded %d disease types\n', length(diseaseTypes));
        
        % Test 3: Treatment Data
        fprintf('3️⃣ Testing TreatmentData...\n');
        treatment = TreatmentData.getTreatmentPlan('High_Severity');
        fprintf('   ✅ Treatment plan loaded for High Severity\n');
        
        % Test 4: Create UI Figure
        fprintf('4️⃣ Testing UI Creation...\n');
        fig = uifigure('Name', 'Arecanut App Test', 'Position', [100 100 800 600]);
        
        % Test 5: Create Navbar
        fprintf('5️⃣ Testing Navbar Component...\n');
        callbacks = struct();
        callbacks.showLoginPopup = @() fprintf('Login popup would show\n');
        callbacks.showSignupPopup = @() fprintf('Signup popup would show\n');
        
        navbar = Navbar(fig, config, callbacks);
        fprintf('   ✅ Navbar created successfully\n');
        
        % Test 6: Create Landing Page
        fprintf('6️⃣ Testing Landing Page Component...\n');
        landingPage = LandingPage(fig, config, callbacks);
        fprintf('   ✅ Landing page created successfully\n');
        
        % Test 7: Create Login Popup
        fprintf('7️⃣ Testing Login Popup Component...\n');
        loginPopup = LoginPopup(fig, config, callbacks);
        fprintf('   ✅ Login popup created successfully\n');
        
        % Test 8: Create Signup Popup
        fprintf('8️⃣ Testing Signup Popup Component...\n');
        signupPopup = SignupPopup(fig, config, callbacks);
        fprintf('   ✅ Signup popup created successfully\n');
        
        % Show the figure
        fig.Visible = 'on';
        
        fprintf('\n🎉 ALL TESTS PASSED!\n');
        fprintf('✨ The new organized application structure works perfectly!\n\n');
        
        fprintf('📋 COMPONENT SUMMARY:\n');
        fprintf('   🎨 AppConfig: Theme and configuration management\n');
        fprintf('   📊 DiseaseData: Disease information database\n');
        fprintf('   💊 TreatmentData: AI treatment recommendations\n');
        fprintf('   🧭 Navbar: Navigation with authentication\n');
        fprintf('   🏠 LandingPage: Home page with disease info\n');
        fprintf('   🔐 LoginPopup: Modal login dialog\n');
        fprintf('   ✨ SignupPopup: Modal registration dialog\n\n');
        
        fprintf('🚀 Ready to launch the full application!\n');
        fprintf('   Run: start_arecanut_app_new\n\n');
        
        % Keep the window open for demonstration
        fprintf('Press any key to close the test window...\n');
        pause;
        close(fig);
        
    catch ME
        fprintf('❌ Test failed: %s\n', ME.message);
        fprintf('Stack trace:\n');
        for i = 1:length(ME.stack)
            fprintf('  %s (line %d)\n', ME.stack(i).name, ME.stack(i).line);
        end
    end
end
