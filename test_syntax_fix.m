function test_syntax_fix()
    % test_syntax_fix - Test if the syntax error is fixed
    
    fprintf('🧪 Testing Syntax Fix...\n\n');
    
    try
        % Add paths
        addpath(genpath('.'));
        
        % Test creating the main app
        fprintf('1️⃣ Testing ArecaApp_New creation...\n');
        
        app = ArecaApp_New();
        
        fprintf('✅ SUCCESS: ArecaApp_New created without syntax errors!\n');
        fprintf('🎉 The ternary operator issue has been resolved.\n\n');
        
        % Test database stats method
        fprintf('2️⃣ Testing database statistics...\n');
        app.showDatabaseStats();
        
        fprintf('\n✅ All syntax issues resolved!\n');
        fprintf('🚀 Application is ready to use.\n\n');
        
        % Clean up
        app.closeApp();
        
    catch ME
        fprintf('❌ ERROR: %s\n', ME.message);
        if ~isempty(ME.stack)
            fprintf('Location: %s (line %d)\n', ME.stack(1).name, ME.stack(1).line);
        end
        
        if contains(ME.message, 'Invalid use of operator')
            fprintf('\n💡 This is likely another ternary operator (?:) issue.\n');
            fprintf('MATLAB does not support the ternary operator syntax.\n');
            fprintf('Use if-else statements instead.\n');
        end
    end
end
