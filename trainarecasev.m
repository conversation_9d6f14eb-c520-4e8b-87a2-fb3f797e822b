clc;
clear;

%% Step 1: Load Dataset
trainPath = 'D:\Areca Project\Sev-pro\train';
testPath  = 'D:\Areca Project\Sev-pro\test';

imdsTrain = imageDatastore(trainPath, ...
    'IncludeSubfolders', true, ...
    'LabelSource', 'foldernames');

imdsTest = imageDatastore(testPath, ...
    'IncludeSubfolders', true, ...
    'LabelSource', 'foldernames');

%% Step 2: Resize Images
inputSize = [224 224 3];
augTrain = augmentedImageDatastore(inputSize, imdsTrain);
augTest = augmentedImageDatastore(inputSize, imdsTest);

%% Step 3: Load Pretrained ResNet-50
net = resnet50;
lgraph = layerGraph(net);

% Check layer names
% analyzeNetwork(net); % Uncomment to explore layer names

% Replace last 3 layers
numClasses = numel(categories(imdsTrain.Labels));
lgraph = replaceLayer(lgraph, 'fc1000', fullyConnectedLayer(numClasses, 'Name', 'fc'));
lgraph = replaceLayer(lgraph, 'ClassificationLayer_fc1000', classificationLayer('Name', 'new_classoutput'));

%% Step 4: Training Options
options = trainingOptions('adam', ...
    'InitialLearnRate',1e-4, ...
    'MaxEpochs',10, ...
    'MiniBatchSize',32, ...
    'Shuffle','every-epoch', ...
    'ValidationData',augTest, ...
    'ValidationFrequency',30, ...
    'Verbose',true, ...
    'Plots','training-progress');

%% Step 5: Train the Network
trainedNet = trainNetwork(augTrain, lgraph, options);

%% Step 6: Save the Model
save('resnet50_arecanut_model.mat', 'trainedNet');

%% Step 7: Evaluate Accuracy on Test Data
predictedLabels = classify(trainedNet, augTest);
actualLabels = imdsTest.Labels;

accuracy = sum(predictedLabels == actualLabels) / numel(actualLabels);
fprintf('\n✅ Test Accuracy: %.2f%%\n', accuracy * 100);

%% Step 8: Confusion Matrix
figure;
confusionchart(actualLabels, predictedLabels);
title('Confusion Matrix - Test Data');
