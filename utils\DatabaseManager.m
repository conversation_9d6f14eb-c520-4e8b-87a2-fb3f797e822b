classdef DatabaseManager < handle
    % DatabaseManager - Unified database interface with MongoDB and local storage
    % Automatically falls back to local storage if MongoDB is not available
    
    properties (Access = private)
        MongoUtils      % MongoDB utilities
        LocalStorage    % Local storage (containers.Map)
        UsesMongoDB     % Flag indicating if MongoDB is being used
        LocalDataFile   % Local data file path
    end
    
    properties (Constant)
        LOCAL_DATA_FILE = 'arecanut_local_data.mat'
    end
    
    methods
        function obj = DatabaseManager(mongoConnectionString, databaseName)
            % Constructor - Initialize database manager
            
            % Set default parameters
            if nargin < 1
                mongoConnectionString = '';
            end
            if nargin < 2
                databaseName = 'arecanut_medical_ai';
            end
            
            obj.LocalDataFile = obj.LOCAL_DATA_FILE;
            obj.UsesMongoDB = false;
            
            % Try to initialize MongoDB first
            obj.initializeMongoDB(mongoConnectionString, databaseName);
            
            % Initialize local storage as backup
            obj.initializeLocalStorage();
            
            % Report which storage system is being used
            obj.reportStorageSystem();
        end
        
        function initializeMongoDB(obj, connectionString, databaseName)
            % Initialize MongoDB connection
            try
                fprintf('🔄 Attempting MongoDB connection...\n');
                obj.MongoUtils = MongoDBUtils(connectionString, databaseName);
                
                if obj.MongoUtils.isConnected()
                    obj.UsesMongoDB = true;
                    fprintf('✅ Using MongoDB for data storage\n');
                else
                    fprintf('⚠️ MongoDB connection failed, using local storage\n');
                end
            catch ME
                fprintf('⚠️ MongoDB initialization failed: %s\n', ME.message);
                fprintf('   Falling back to local storage\n');
            end
        end
        
        function initializeLocalStorage(obj)
            % Initialize local storage system
            try
                obj.LocalStorage = struct();
                obj.LocalStorage.users = containers.Map();
                obj.LocalStorage.detections = containers.Map();
                obj.LocalStorage.sessions = containers.Map();
                
                % Load existing local data if available
                if exist(obj.LocalDataFile, 'file')
                    obj.loadLocalData();
                else
                    obj.createDefaultData();
                end
                
                fprintf('✅ Local storage initialized\n');
            catch ME
                fprintf('❌ Local storage initialization failed: %s\n', ME.message);
                obj.createDefaultData();
            end
        end
        
        function loadLocalData(obj)
            % Load data from local file
            try
                loadedData = load(obj.LocalDataFile);
                
                if isfield(loadedData, 'localData')
                    data = loadedData.localData;
                    
                    if isfield(data, 'users') && isa(data.users, 'containers.Map')
                        obj.LocalStorage.users = data.users;
                    end
                    if isfield(data, 'detections') && isa(data.detections, 'containers.Map')
                        obj.LocalStorage.detections = data.detections;
                    end
                    if isfield(data, 'sessions') && isa(data.sessions, 'containers.Map')
                        obj.LocalStorage.sessions = data.sessions;
                    end
                    
                    fprintf('📂 Local data loaded successfully\n');
                else
                    obj.createDefaultData();
                end
            catch ME
                fprintf('⚠️ Error loading local data: %s\n', ME.message);
                obj.createDefaultData();
            end
        end
        
        function createDefaultData(obj)
            % Create default data including test users
            try
                % Create default test user
                testUser = struct();
                testUser.username = 'testuser';
                testUser.name = 'Test User';
                testUser.email = '<EMAIL>';
                testUser.phone = '1234567890';
                testUser.password = 'test123';
                testUser.created_at = datetime('now');
                testUser.status = 'active';
                testUser.role = 'user';
                
                obj.LocalStorage.users('testuser') = testUser;
                
                % Create admin user
                adminUser = struct();
                adminUser.username = 'admin';
                adminUser.name = 'Administrator';
                adminUser.email = '<EMAIL>';
                adminUser.phone = '9876543210';
                adminUser.password = 'admin123';
                adminUser.created_at = datetime('now');
                adminUser.status = 'active';
                adminUser.role = 'admin';
                
                obj.LocalStorage.users('admin') = adminUser;
                
                fprintf('👤 Default users created (testuser/test123, admin/admin123)\n');
                
                % Save to file
                obj.saveLocalData();
            catch ME
                fprintf('❌ Error creating default data: %s\n', ME.message);
            end
        end
        
        function saveLocalData(obj)
            % Save local data to file
            try
                localData = obj.LocalStorage;
                save(obj.LocalDataFile, 'localData');
                fprintf('💾 Local data saved\n');
            catch ME
                fprintf('⚠️ Error saving local data: %s\n', ME.message);
            end
        end
        
        function reportStorageSystem(obj)
            % Report which storage system is being used
            fprintf('\n📊 DATABASE CONFIGURATION:\n');
            if obj.UsesMongoDB
                fprintf('   🗄️ Primary: MongoDB (Connected)\n');
                fprintf('   💾 Backup: Local Storage (Available)\n');
                stats = obj.MongoUtils.getDatabaseStats();
                if isfield(stats, 'total_users')
                    fprintf('   📈 MongoDB Stats: %d users, %d detections\n', ...
                        stats.total_users, stats.total_detections);
                end
            else
                fprintf('   💾 Primary: Local Storage (Active)\n');
                fprintf('   🗄️ MongoDB: Not available\n');
                fprintf('   📈 Local Stats: %d users, %d detections\n', ...
                    obj.LocalStorage.users.Count, obj.LocalStorage.detections.Count);
            end
            fprintf('\n');
        end
        
        function success = insertUser(obj, userData)
            % Insert user into database
            success = false;
            
            try
                if obj.UsesMongoDB
                    success = obj.MongoUtils.insertUser(userData);
                    if ~success
                        fprintf('⚠️ MongoDB insert failed, trying local storage\n');
                        success = obj.insertUserLocal(userData);
                    end
                else
                    success = obj.insertUserLocal(userData);
                end
            catch ME
                fprintf('❌ Error inserting user: %s\n', ME.message);
            end
        end
        
        function success = insertUserLocal(obj, userData)
            % Insert user into local storage
            success = false;
            
            try
                if obj.LocalStorage.users.isKey(userData.username)
                    fprintf('❌ Username %s already exists\n', userData.username);
                    return;
                end
                
                userData.created_at = datetime('now');
                userData.updated_at = datetime('now');
                userData.status = 'active';

                % Set default password if not provided
                if ~isfield(userData, 'password') || isempty(userData.password)
                    userData.password = 'demo123';
                end
                
                obj.LocalStorage.users(userData.username) = userData;
                obj.saveLocalData();
                
                success = true;
                fprintf('✅ User %s inserted into local storage\n', userData.username);
            catch ME
                fprintf('❌ Error inserting user locally: %s\n', ME.message);
            end
        end
        
        function user = findUser(obj, username)
            % Find user by username
            user = [];
            
            try
                if obj.UsesMongoDB
                    user = obj.MongoUtils.findUser(username);
                    if isempty(user)
                        user = obj.findUserLocal(username);
                    end
                else
                    user = obj.findUserLocal(username);
                end
            catch ME
                fprintf('❌ Error finding user: %s\n', ME.message);
            end
        end
        
        function user = findUserLocal(obj, username)
            % Find user in local storage
            user = [];
            
            try
                if obj.LocalStorage.users.isKey(username)
                    user = obj.LocalStorage.users(username);
                    fprintf('✅ User %s found in local storage\n', username);
                else
                    fprintf('❌ User %s not found in local storage\n', username);
                end
            catch ME
                fprintf('❌ Error finding user locally: %s\n', ME.message);
            end
        end
        
        function success = authenticateUser(obj, username, password)
            % Authenticate user credentials
            success = false;
            
            try
                user = obj.findUser(username);
                if ~isempty(user) && isfield(user, 'password')
                    success = strcmp(user.password, password);
                    if success
                        fprintf('✅ User %s authenticated successfully\n', username);
                        obj.logUserSession(username);
                    else
                        fprintf('❌ Invalid password for user %s\n', username);
                    end
                else
                    fprintf('❌ User %s not found\n', username);
                end
            catch ME
                fprintf('❌ Authentication error: %s\n', ME.message);
            end
        end
        
        function success = insertDetection(obj, detectionData)
            % Insert detection result
            success = false;
            
            try
                if obj.UsesMongoDB
                    success = obj.MongoUtils.insertDetection(detectionData);
                    if ~success
                        success = obj.insertDetectionLocal(detectionData);
                    end
                else
                    success = obj.insertDetectionLocal(detectionData);
                end
            catch ME
                fprintf('❌ Error inserting detection: %s\n', ME.message);
            end
        end
        
        function success = insertDetectionLocal(obj, detectionData)
            % Insert detection into local storage
            success = false;
            
            try
                detectionId = char(java.util.UUID.randomUUID());
                detectionData.id = detectionId;
                detectionData.timestamp = datetime('now');
                
                obj.LocalStorage.detections(detectionId) = detectionData;
                obj.saveLocalData();
                
                success = true;
                fprintf('✅ Detection result saved locally\n');
            catch ME
                fprintf('❌ Error saving detection locally: %s\n', ME.message);
            end
        end
        
        function logUserSession(obj, username)
            % Log user session
            try
                sessionData = struct();
                sessionData.username = username;
                sessionData.login_time = datetime('now');
                sessionData.session_id = char(java.util.UUID.randomUUID());
                
                if obj.UsesMongoDB
                    % Log to MongoDB if available
                    % obj.MongoUtils.insertSession(sessionData);
                end
                
                % Always log locally
                obj.LocalStorage.sessions(sessionData.session_id) = sessionData;
                obj.saveLocalData();
                
                fprintf('📝 User session logged\n');
            catch ME
                fprintf('⚠️ Session logging warning: %s\n', ME.message);
            end
        end
        
        function stats = getDatabaseStats(obj)
            % Get comprehensive database statistics
            stats = struct();
            
            try
                if obj.UsesMongoDB
                    mongoStats = obj.MongoUtils.getDatabaseStats();
                    stats.mongodb = mongoStats;
                end
                
                % Local storage stats
                stats.local_storage = struct();
                stats.local_storage.users = obj.LocalStorage.users.Count;
                stats.local_storage.detections = obj.LocalStorage.detections.Count;
                stats.local_storage.sessions = obj.LocalStorage.sessions.Count;
                
                if obj.UsesMongoDB
                    stats.primary_storage = 'mongodb';
                else
                    stats.primary_storage = 'local';
                end
                stats.mongodb_available = obj.UsesMongoDB;
                
            catch ME
                fprintf('❌ Error getting database stats: %s\n', ME.message);
                stats.error = ME.message;
            end
        end
        
        function disconnect(obj)
            % Disconnect from database
            try
                if obj.UsesMongoDB && ~isempty(obj.MongoUtils)
                    obj.MongoUtils.disconnect();
                end
                obj.saveLocalData();
                fprintf('✅ Database disconnected and data saved\n');
            catch ME
                fprintf('⚠️ Disconnect warning: %s\n', ME.message);
            end
        end
        
        function delete(obj)
            % Destructor
            obj.disconnect();
        end
    end
end
