classdef MongoDBUtils < handle
    % MongoDBUtils - MongoDB connection and operations utility
    % Handles all database operations for the Arecanut Disease Detection Platform
    
    properties (Access = private)
        Connection      % MongoDB connection object
        DatabaseName    % Database name
        IsConnected     % Connection status
        ConnectionString % MongoDB connection string
    end
    
    properties (Constant)
        % Collection names
        USERS_COLLECTION = 'users'
        DETECTIONS_COLLECTION = 'detections'
        TREATMENTS_COLLECTION = 'treatments'
        SESSIONS_COLLECTION = 'sessions'
        
        % Default connection settings
        DEFAULT_HOST = 'localhost'
        DEFAULT_PORT = 27017
        DEFAULT_DATABASE = 'arecanut_medical_ai'
    end
    
    methods
        function obj = MongoDBUtils(connectionString, databaseName)
            % Constructor - Initialize MongoDB connection
            if nargin < 1 || isempty(connectionString)
                connectionString = sprintf('mongodb://%s:%d', ...
                    obj.DEFAULT_HOST, obj.DEFAULT_PORT);
            end
            if nargin < 2 || isempty(databaseName)
                databaseName = obj.DEFAULT_DATABASE;
            end
            
            obj.ConnectionString = connectionString;
            obj.DatabaseName = databaseName;
            obj.IsConnected = false;
            
            % Attempt to connect
            obj.connect();
        end
        
        function success = connect(obj)
            % Establish connection to MongoDB
            success = false;
            
            try
                fprintf('🔌 Connecting to MongoDB...\n');
                fprintf('   Host: %s\n', obj.ConnectionString);
                fprintf('   Database: %s\n', obj.DatabaseName);
                
                % Check if MongoDB Java driver is available
                if ~obj.checkMongoDBDriver()
                    fprintf('⚠️ MongoDB Java driver not found, using local storage fallback\n');
                    return;
                end
                
                % Create MongoDB connection
                obj.Connection = mongodb(obj.ConnectionString, obj.DatabaseName);
                
                % Test connection
                if obj.testConnection()
                    obj.IsConnected = true;
                    success = true;
                    fprintf('✅ MongoDB connected successfully\n');
                    
                    % Initialize collections
                    obj.initializeCollections();
                else
                    fprintf('❌ MongoDB connection test failed\n');
                end
                
            catch ME
                fprintf('⚠️ MongoDB connection failed: %s\n', ME.message);
                fprintf('   Falling back to local storage\n');
                obj.IsConnected = false;
            end
        end
        
        function connected = isConnected(obj)
            % Check if MongoDB is connected
            connected = obj.IsConnected && ~isempty(obj.Connection);
        end
        
        function success = testConnection(obj)
            % Test MongoDB connection
            success = false;
            try
                if ~isempty(obj.Connection)
                    % Try to list collections
                    collections = obj.Connection.CollectionNames;
                    success = true;
                end
            catch
                success = false;
            end
        end
        
        function available = checkMongoDBDriver(obj)
            % Check if MongoDB Java driver is available
            available = false;
            try
                % Try to create a MongoDB connection object
                javaaddpath(fullfile(matlabroot, 'java', 'jarext', 'mongo-java-driver.jar'));
                available = true;
            catch
                try
                    % Alternative: check if mongodb function exists
                    if exist('mongodb', 'file') == 2
                        available = true;
                    end
                catch
                    available = false;
                end
            end
        end
        
        function initializeCollections(obj)
            % Initialize required collections with indexes
            try
                collections = {obj.USERS_COLLECTION, obj.DETECTIONS_COLLECTION, ...
                              obj.TREATMENTS_COLLECTION, obj.SESSIONS_COLLECTION};
                
                for i = 1:length(collections)
                    collectionName = collections{i};
                    
                    % Create collection if it doesn't exist
                    if ~any(strcmp(obj.Connection.CollectionNames, collectionName))
                        fprintf('📝 Creating collection: %s\n', collectionName);
                    end
                    
                    % Create indexes based on collection type
                    obj.createIndexes(collectionName);
                end
                
                fprintf('✅ Collections initialized\n');
            catch ME
                fprintf('⚠️ Collection initialization warning: %s\n', ME.message);
            end
        end
        
        function createIndexes(obj, collectionName)
            % Create appropriate indexes for each collection
            try
                switch collectionName
                    case obj.USERS_COLLECTION
                        % Index on username (unique)
                        % obj.Connection.createIndex(collectionName, 'username', 'unique', true);
                        
                    case obj.DETECTIONS_COLLECTION
                        % Index on user_id and timestamp
                        % obj.Connection.createIndex(collectionName, 'user_id');
                        % obj.Connection.createIndex(collectionName, 'timestamp');
                        
                    case obj.SESSIONS_COLLECTION
                        % Index on session_id and user_id
                        % obj.Connection.createIndex(collectionName, 'session_id', 'unique', true);
                end
            catch ME
                fprintf('⚠️ Index creation warning: %s\n', ME.message);
            end
        end
        
        function success = insertUser(obj, userData)
            % Insert new user into database
            success = false;
            
            try
                if obj.isConnected()
                    % Add metadata
                    userData.created_at = datetime('now');
                    userData.updated_at = datetime('now');
                    userData.status = 'active';
                    
                    % Insert into MongoDB
                    result = insert(obj.Connection, obj.USERS_COLLECTION, userData);
                    success = ~isempty(result);
                    
                    if success
                        fprintf('✅ User %s inserted into MongoDB\n', userData.username);
                    end
                else
                    fprintf('⚠️ MongoDB not connected, cannot insert user\n');
                end
            catch ME
                fprintf('❌ Error inserting user: %s\n', ME.message);
            end
        end
        
        function user = findUser(obj, username)
            % Find user by username
            user = [];
            
            try
                if obj.isConnected()
                    query = struct('username', username);
                    result = find(obj.Connection, obj.USERS_COLLECTION, query);
                    
                    if ~isempty(result)
                        user = result(1); % Return first match
                        fprintf('✅ User %s found in MongoDB\n', username);
                    else
                        fprintf('❌ User %s not found in MongoDB\n', username);
                    end
                else
                    fprintf('⚠️ MongoDB not connected, cannot find user\n');
                end
            catch ME
                fprintf('❌ Error finding user: %s\n', ME.message);
            end
        end
        
        function success = updateUser(obj, username, updateData)
            % Update user data
            success = false;
            
            try
                if obj.isConnected()
                    query = struct('username', username);
                    updateData.updated_at = datetime('now');
                    
                    result = update(obj.Connection, obj.USERS_COLLECTION, query, updateData);
                    success = result.ModifiedCount > 0;
                    
                    if success
                        fprintf('✅ User %s updated in MongoDB\n', username);
                    end
                else
                    fprintf('⚠️ MongoDB not connected, cannot update user\n');
                end
            catch ME
                fprintf('❌ Error updating user: %s\n', ME.message);
            end
        end
        
        function success = insertDetection(obj, detectionData)
            % Insert detection result into database
            success = false;
            
            try
                if obj.isConnected()
                    % Add metadata
                    detectionData.timestamp = datetime('now');
                    detectionData.id = char(java.util.UUID.randomUUID());
                    
                    % Insert into MongoDB
                    result = insert(obj.Connection, obj.DETECTIONS_COLLECTION, detectionData);
                    success = ~isempty(result);
                    
                    if success
                        fprintf('✅ Detection result inserted into MongoDB\n');
                    end
                else
                    fprintf('⚠️ MongoDB not connected, cannot insert detection\n');
                end
            catch ME
                fprintf('❌ Error inserting detection: %s\n', ME.message);
            end
        end
        
        function detections = getUserDetections(obj, username, limit)
            % Get user's detection history
            detections = [];
            
            if nargin < 3
                limit = 50; % Default limit
            end
            
            try
                if obj.isConnected()
                    query = struct('username', username);
                    options = struct('limit', limit, 'sort', struct('timestamp', -1));
                    
                    detections = find(obj.Connection, obj.DETECTIONS_COLLECTION, query, options);
                    
                    fprintf('✅ Retrieved %d detections for user %s\n', length(detections), username);
                else
                    fprintf('⚠️ MongoDB not connected, cannot retrieve detections\n');
                end
            catch ME
                fprintf('❌ Error retrieving detections: %s\n', ME.message);
            end
        end
        
        function stats = getDatabaseStats(obj)
            % Get database statistics
            stats = struct();
            
            try
                if obj.isConnected()
                    % Get collection counts
                    stats.total_users = count(obj.Connection, obj.USERS_COLLECTION);
                    stats.total_detections = count(obj.Connection, obj.DETECTIONS_COLLECTION);
                    stats.database_name = obj.DatabaseName;
                    stats.connection_status = 'connected';
                    
                    fprintf('📊 Database Stats: %d users, %d detections\n', ...
                        stats.total_users, stats.total_detections);
                else
                    stats.connection_status = 'disconnected';
                    fprintf('⚠️ MongoDB not connected, cannot get stats\n');
                end
            catch ME
                fprintf('❌ Error getting database stats: %s\n', ME.message);
                stats.connection_status = 'error';
                stats.error_message = ME.message;
            end
        end
        
        function disconnect(obj)
            % Disconnect from MongoDB
            try
                if obj.isConnected()
                    close(obj.Connection);
                    obj.IsConnected = false;
                    fprintf('✅ MongoDB disconnected\n');
                end
            catch ME
                fprintf('⚠️ Error disconnecting: %s\n', ME.message);
            end
        end
        
        function delete(obj)
            % Destructor - ensure connection is closed
            obj.disconnect();
        end
    end
    
    methods (Static)
        function instance = getInstance()
            % Singleton pattern for MongoDB connection
            persistent mongoInstance;
            
            if isempty(mongoInstance) || ~isvalid(mongoInstance)
                mongoInstance = MongoDBUtils();
            end
            
            instance = mongoInstance;
        end
        
        function success = testMongoDBAvailability()
            % Test if MongoDB is available and accessible
            success = false;
            
            try
                testUtils = MongoDBUtils();
                success = testUtils.isConnected();
                testUtils.disconnect();
            catch
                success = false;
            end
        end
    end
end
