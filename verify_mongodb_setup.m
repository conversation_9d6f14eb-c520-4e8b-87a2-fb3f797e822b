function verify_mongodb_setup()
    % verify_mongodb_setup - Quick verification of MongoDB integration
    
    fprintf('\n🔍 VERIFYING MONGODB SETUP\n');
    fprintf('==========================\n\n');
    
    try
        % Add paths
        addpath(genpath('.'));
        
        % Test 1: Check if files exist
        fprintf('1️⃣ Checking required files...\n');
        
        files = {'utils/MongoDBUtils.m', 'utils/DatabaseManager.m', 'ArecaApp_New.m'};
        allFilesExist = true;
        
        for i = 1:length(files)
            if exist(files{i}, 'file')
                fprintf('   ✅ %s\n', files{i});
            else
                fprintf('   ❌ %s (missing)\n', files{i});
                allFilesExist = false;
            end
        end
        
        if ~allFilesExist
            fprintf('   ❌ Some required files are missing!\n');
            return;
        end
        
        % Test 2: Test DatabaseManager creation
        fprintf('\n2️⃣ Testing DatabaseManager...\n');
        
        dbManager = DatabaseManager();
        fprintf('   ✅ DatabaseManager created successfully\n');
        
        % Test 3: Test basic operations
        fprintf('\n3️⃣ Testing basic database operations...\n');
        
        % Test user insertion
        testUser = struct();
        testUser.username = 'verifytest';
        testUser.name = 'Verification Test';
        testUser.email = '<EMAIL>';
        testUser.phone = '1111111111';
        
        success = dbManager.insertUser(testUser);
        if success
            fprintf('   User insertion: ✅ SUCCESS\n');
        else
            fprintf('   User insertion: ❌ FAILED\n');
        end

        % Test authentication
        success = dbManager.authenticateUser('verifytest', 'demo123');
        if success
            fprintf('   Authentication: ✅ SUCCESS\n');
        else
            fprintf('   Authentication: ❌ FAILED\n');
        end
        
        % Test detection insertion
        detection = struct();
        detection.username = 'verifytest';
        detection.disease_type = 'Medium_Severity';
        detection.confidence = 0.88;
        detection.image_path = '/test/verify.jpg';
        
        success = dbManager.insertDetection(detection);
        if success
            fprintf('   Detection insertion: ✅ SUCCESS\n');
        else
            fprintf('   Detection insertion: ❌ FAILED\n');
        end
        
        % Test 4: Get statistics
        fprintf('\n4️⃣ Database statistics...\n');
        stats = dbManager.getDatabaseStats();
        
        fprintf('   Storage type: %s\n', stats.primary_storage);
        if stats.mongodb_available
            fprintf('   MongoDB available: Yes\n');
        else
            fprintf('   MongoDB available: No\n');
        end
        
        if isfield(stats, 'local_storage')
            fprintf('   Local users: %d\n', stats.local_storage.users);
            fprintf('   Local detections: %d\n', stats.local_storage.detections);
        end
        
        % Clean up
        dbManager.disconnect();
        
        fprintf('\n✅ VERIFICATION COMPLETE!\n\n');
        
        fprintf('📋 SETUP STATUS:\n');
        if stats.mongodb_available
            fprintf('   🗄️ MongoDB: Connected and working\n');
            fprintf('   💾 Local Storage: Available as backup\n');
            fprintf('   🎯 Data will be stored in MongoDB\n');
        else
            fprintf('   🗄️ MongoDB: Not available (this is normal if not installed)\n');
            fprintf('   💾 Local Storage: Active and working\n');
            fprintf('   🎯 Data will be stored locally\n');
        end
        
        fprintf('\n🚀 READY TO USE:\n');
        fprintf('   >> run_app\n');
        fprintf('   or\n');
        fprintf('   >> app = ArecaApp_New()\n\n');
        
        fprintf('👤 DEFAULT CREDENTIALS:\n');
        fprintf('   Username: testuser\n');
        fprintf('   Password: test123\n');
        fprintf('   Username: admin\n');
        fprintf('   Password: admin123\n\n');
        
    catch ME
        fprintf('❌ VERIFICATION FAILED: %s\n', ME.message);
        if ~isempty(ME.stack)
            fprintf('   Location: %s (line %d)\n', ME.stack(1).name, ME.stack(1).line);
        end
        
        fprintf('\n🔧 TROUBLESHOOTING:\n');
        fprintf('   1. Ensure all files are in the correct folders\n');
        fprintf('   2. Run: addpath(genpath(pwd))\n');
        fprintf('   3. Check MATLAB version compatibility\n');
        fprintf('   4. Verify folder structure is correct\n\n');
    end
end
